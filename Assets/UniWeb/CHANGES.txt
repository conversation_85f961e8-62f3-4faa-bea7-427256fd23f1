-10/09/2014
Made a cross platform TcpClient class to remove WP8 shenanigans with prebuilt DLL.

-02/09/2014
Bugfix for certain chunked-encoding transfers.
Removed Ionic ZLib dependency.

-14/04/2014
Added experimental KeepAlive support. Uncomment #USE_KEEPALIVE in Request.cs to enable.

-08/04/2014
Added a tiny embeddable web server with REST style components.
UniWeb now includes the UniExtender package in DLL form. Source code is available.
See: https://www.assetstore.unity3d.com/#/content/2597

-03/04/2014
Added new TcpClient from SocketEx, and BouncyCastle crypto library.
This means we should have full WP8 support!

-24/03/2014
Corrected proxy support on mobile devices.

-07/03/2014
Added proxy support.
Added FAQ.

- 28/02/2014
Added demo server scripts for nodejs.
Added exception feedback to WebSocket.
Added SocketIOClient and WebSocketClient examples.

- 03/01/2014
Added default Accept header if not specified.
Removed Authorisation header if uri.UserInfo is null or == ""

- 05/12/2013
Added new ZLib DLL that allows compilation with .NET 2.0 Subset.
Added comments showing where to implement SSL certificate check.

- 26/11/2013
Fixed Disposed object exception when using GZip.
Fixed Redirect Handling (change Method to "GET" on 301, 302, leave unchanged on 307)

- 22/11/2013
Refactoring Marathon:
    Modified Socket.IO to be closer to the offical API.
    Merged all examples into one file.
    Removed obsolete methods.
    Removed some extra methods, so there should only be one way to do something. (or 2 ;-)

Bugs Fixed:
    Dead requests when coming back from sleep on iOS
    Added close messages to discarded websockets.


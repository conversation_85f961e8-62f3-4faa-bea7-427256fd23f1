using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CodeStage.AntiCheat.ObscuredTypes;
using DG.Tweening;

public class Huawei_CheckOrder : MonoBehaviour
{

    string productId = "";

    private IAppsTeam_Huawei huawei;
    int showCount = 0;

    // Use this for initialization
    void Awake()
    {
        if (huawei == null)
        {
            
            huawei = new GameObject("Huawei_JavaObject").AddComponent<IAppsTeam_Huawei>();
        }
    }

    void Start()
    {
        productId = GlobalVariable_Android.FullVersion_Flag;

        Init();
    }

    void Init()
    {
        showCount = 0;
        huawei.SetCallbackObjectName(gameObject.name);

        huawei.CheckUpate();

        //huawei.GetPurchaseInfo(productId);
    }

    public void HideProgressDialog(string result)
    {
        //NativeDialogs.Instance.HideProgressDialog();
    }

    public void GetPayProductResult(string result)
    {

        if (result == "ORDER_STATE_SUCCESS:" + productId)
        {
            Debug.Log("CheckOrder Pay Success: " + result);

            string msgContent = GameUtility.LocalizedString("UnlockSuccess");

            ObscuredPrefs.SetBool(GlobalVariable_Android.PrefsBool, true);

            ObscuredPrefs.Save();

            Debug.Log("成功恢复订单-解锁: " + ObscuredPrefs.GetBool(GlobalVariable_Android.PrefsBool, false) + "\n");

            GlobalVariable_Android.isLock = false;

            if (ObscuredPrefs.GetBool(GlobalVariable_Android.PrefsBool) == true)
            {
                Debug.Log("unlock");
            }
            else
            {
                Debug.Log("lock");
            }
        }
        else
        {
            Debug.Log("Pay Failed: " + result);
        }

    }


    public void ShowErrorResult(string result)
    {
        //订单交易状态。-1：初始化, 0：已购买,  1：已取消, 2：已退款
        Debug.Log("CheckOrder ShowErrorResult: " + result);

        if (result == "60050")
        {
            Debug.Log("未登录华为帐号。");

        }
        else if (result == "-1006")
        {
            Debug.Log("重复调用接口");

            string msgContent = "重复调用接口, 支付界面已打开";

            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");

            Debug.Log(msgContent);

            //ShowMessageBox(msgTitle, msgContent, strA);
        }
        else if (result == "30000")
        {
            Debug.Log("用户取消支付");
        }
        else if (result == "30005")
        {
            string msgContent = "网络连接异常";

            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");

            Debug.Log(msgContent);
            //ShowMessageBox(msgTitle, msgContent, strA);
        }
        else if (result == "30002")
        {
            string msgContent = "支付结果查询超时";

            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");
            Debug.Log(msgContent);
            //ShowMessageBox(msgTitle, msgContent, strA);
        }
        else
        {
            string msgContent = result;

            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");
            Debug.Log(msgContent);
            //ShowMessageBox(msgTitle, msgContent, strA);
        }

    }

    void ShowMessageBox(string title, string msgContent, string strBtn)
    {
        /*
        NativeDialogs.Instance.ShowMessageBox(title, msgContent, new string[] { strBtn }, false,
                                              (string b) => {

                                              });
                                              */
        Debug.Log(msgContent);
    }

    public void NoPurchaseRecord(string result)
    {
        showCount++;
        if(showCount == 1) Debug.Log(result);
    }
}


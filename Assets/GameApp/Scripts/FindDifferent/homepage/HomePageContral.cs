using UnityEngine;
using System.Collections;
using DG.Tweening;
using CodeStage.AntiCheat.ObscuredTypes;

public class HomePageContral : MonoBehaviour {
	public GameObject title, person1, parent, start1, shoppingCartBtn;
    public Vector3 tit, per, par, st1;

    public string prefsEncryptionKey = "change me!";

    
	// Use this for initialization
	void Start () {
	
        GlobalVariable_Android.viewPolicyFromPage = GlobalVariable_Android.ViewPolicyFromPage.Home;

        #if UNITY_ANDROID
        GlobalVariable.is_IAP = false;

        #endif


        CheckGameLevelUpdate();
		
		GlobalVariable.mapOfButtonHasClick = false;
	
	
        tit = title.transform.position;
        per = person1.transform.position;
        //par = parent.transform.position;
        st1 = start1.transform.localScale;
        person1.transform.Translate(7, 0, 0);
        title.transform.Translate(0, 5, 0);
        PersonIn();
	
        CheckLevelLock();

        if(LevelUnLock())
		{
			shoppingCartBtn.SetActive(false);
		}
		else
		{
			shoppingCartBtn.SetActive(true);
		}
		
        GlobalVariable.paidAds_FromPage = GlobalVariable.PaidAds_FromPage.Home;
	}

    bool LevelUnLock()
    {
        return ObscuredPrefs.GetBool(GlobalVariable_Android.PrefsBool, false);
    }

    void CheckGameLevelUpdate()
    {
        LoadAppConfig loadAppConfig_Control = FindObjectOfType<LoadAppConfig>();
        if (loadAppConfig_Control)
        {
            loadAppConfig_Control.ReDownData();
        }
    }

    void CheckLevelLock()
    {
        ObscuredPrefs.CryptoKey = prefsEncryptionKey;

    }

    void PersonIn()
    {
        /*
        TweenParms parms = new TweenParms();
        parms.Prop("position", per);
        parms.AutoKill(true);
        parms.Ease(EaseType.Linear);
        parms.Loops(1,LoopType.Yoyo);
        parms.OnComplete(PersonMove);
        parms.Delay(0);
        HOTween.To(person1.transform, 1, parms);
*/
        Vector3 pos = per;
        person1.transform.DOMove(pos, 1).OnComplete(PersonMove)
           .SetDelay(0);
    }

    void PersonMove()
    {
        /*
        TweenParms parms = new TweenParms();
        parms.Prop("position", new Vector3(person1.transform.position.x, person1.transform.position.y-0.2f, person1.transform.position.z));
        parms.AutoKill(true);
        parms.Ease(EaseType.Linear);
        parms.Loops(-1, LoopType.Yoyo);
        parms.Delay(0);
        HOTween.To(person1.transform, 1, parms);
*/

        Vector3 pos = new Vector3(person1.transform.position.x, person1.transform.position.y - 0.2f, person1.transform.position.z);
        person1.transform.DOMove(pos, 1).OnComplete(PersonMove).SetEase(Ease.Linear)
               .SetDelay(0).SetLoops(-1, LoopType.Yoyo);

        Invoke("TitlceIn", 0.1f);
    }

    void TitlceIn()
    {
        /*
        TweenParms parms = new TweenParms();
        parms.Prop("position", tit);
        parms.AutoKill(true);
        parms.Ease(EaseType.EaseOutBack);
        parms.Loops(1, LoopType.Yoyo);
        parms.OnComplete(StartPlay);
        parms.Delay(0);
        HOTween.To(title.transform ,0.5f,parms);
*/

        Vector3 pos = GameUtility.VT_AddY(title.transform.position, -5);
        title.transform.DOMove(pos, 0.5f).OnComplete(StartPlay)
               .SetDelay(0).SetEase(Ease.OutBack);
    }

    void StartPlay()
    {
        /*
        TweenParms parms = new TweenParms();
        parms.Prop("localScale",new Vector3(1.1f,1.1f,1.1f));
        parms.AutoKill(true);
        parms.Ease(EaseType.Linear);
        parms.Loops(-1, LoopType.Yoyo);
        parms.Delay(0);
        HOTween.To(start1.transform, 1, parms);
*/
 
        start1.transform.DOScale(1.1f, 1).SetEase(Ease.Linear)
               .SetDelay(0).SetLoops(-1, LoopType.Yoyo);
    }

    
	void ClickPlay()
	{
		if(GlobalVariable.mapOfButtonHasClick) return;
		GlobalVariable.mapOfButtonHasClick = true;
		

		
		GameUtility.GoToSceneName("Level_Select_Package");
	}
	
	void RemoveAdsClick()
	{
        /*
		string msgContent = GameUtility.LocalizedString("title2");
		string btn1 = GameUtility.LocalizedString("Cancel");
		string btn2 = GameUtility.LocalizedString("Submit");
		NativeDialogs.Instance.ShowMessageBox("", msgContent, new string[]{btn1, btn2}, false, (string b) => {
			
			if(b == btn2)
			{
				GoToParentZoneClick();
			}
			
		});
		*/
        GameUtility.GoToSceneName_NotAddSuffix("A_PaidAds");
		
	}
	
	void GoToParentZoneClick()
	{
		string msgTitle = GameUtility.LocalizedString("ForParents");
		int answer = 0;
		string msgContent = msgTitle + GameUtility.LocalizedString("ForParentsQT") + GameUtility.GetParentQuestion(out answer);
		string strA = GameUtility.LocalizedString("Cancel");
		string strB = GameUtility.LocalizedString("Submit");
		NativeDialogs.Instance.ShowPromptMessageBox("", msgContent, new string[] {strA, strB}, true, 
		(string prompt, string button) =>  {
			
			
			if(answer.ToString() == prompt)
			{	
				GlobalVariable.mapOfButtonHasClick = true;
				
				//if(ourAdsData) ourAdsData.OurAdsMoveOutScreen();
				
				//IAppsTeamIOSUntil.MobClickEvent("ParentZoneScene");
				GameManager.Instance().SaveParentsZoneNewMsgFlag();	
				
				if(AspectRatios.GetAspectRatio() == AspectRatio.Aspect4by3)
				{
					Application.LoadLevel("ParentsZone");
				}
				else
				{
					Application.LoadLevel("ParentsZone-Phone");
				}
				
			}
			
		});
		
	}

    void MoreApp_Click()
    {
        GameUtility.GoToSceneName_NotAddSuffix("AppsList");
    }
	

	// Update is called once per frame
	/*
	void Update () {
	
	}
	*/
}
using UnityEngine;
using FairyGUI;
using FairyGUI.Utils;
using MiniJSON_New;
using DG.Tweening;
using LitJson;
using System.Globalization;
using FD1_UI.FD1_LevelPage;
using System.Collections;
using Mini_GameFramework.Utils;
using Mini_GameFramework.UI;
using System.Collections.Generic;





public class FD1_Levels : MonoBehaviour
{
    public string packageName = "FD1_LevelPage";
    public AudioClip btnClickSound;

    private GComponent _mainView;
    private float current_scrollPane_posX = 0;
    private GList _list;
    private JsonData app_config_data;
    private string current_category;
    private string[] row_level_array;
    private int _current_page_no = 1;
    private int _total_page_count;
    private int _per_page_count;
    private int _level_count;
    private string card_packageName;

    private bool isGoingNextScene = false;

    private AudioSource audioSource;

    private GComponent _viewMain;

    private GComponent _pageControl;

    private GComponent m_TopNav;

    // 存储锁图标动画序列的字典
    private Dictionary<GObject, Sequence> _lockAnimations = new Dictionary<GObject, Sequence>();

    // 存储可用关卡图标动画序列的字典
    private Dictionary<GObject, Sequence> _availableLevelAnimations = new Dictionary<GObject, Sequence>();

    // 存储需要显示动画的关卡ID集合
    private HashSet<string> _animatedLevelIds = new HashSet<string>();


    // Start is called before the first frame update
    void Start()
    {

        GameProgressManager.Instance.Initialize("FD1_SpotDifference");

        audioSource = gameObject.AddComponent<AudioSource>();
        audioSource.spatialBlend = 0;

        current_category = FD1_GlobalVariable.current_category;
        Debug.Log($"current_category:{current_category}");
        FGUI_DataInit();

        // 在初始化后，检查是否有新解锁的关卡并自动翻页
        StartCoroutine(CheckForNewlyUnlockedLevels());
    }

    void FGUI_DataInit()
    {
        // 清空需要显示动画的关卡ID集合
        _animatedLevelIds.Clear();

        string str_app_config_data = Resources.Load("Configs/Data_FD1_LevelPage").ToString();
        app_config_data = JsonMapper.ToObject(str_app_config_data);

        GRoot.inst.SetContentScaleFactor(1852, 854, UIContentScaler.ScreenMatchMode.MatchWidthOrHeight);

        UIPackage package = UIPackage.AddPackage($"FGUI/{packageName}");
        foreach (var item in package.dependencies)
        {
            UIPackage.AddPackage($"FGUI/{item["name"]}");
        }

        //FD1_LevelPageBinder.BindAll();
        //_mainView = UI_FD1_Levels.CreateInstance();
        //_mainView.fairyBatching = true;
        //_mainView.SetSize(GRoot.inst.width, GRoot.inst.height);
        //_mainView.AddRelation(GRoot.inst, RelationType.Size);
        //GRoot.inst.AddChild(_mainView);

        //_viewMain = _mainView as UI_FD1_Levels;

        UIPanel uiPanel = FindObjectOfType<UIPanel>();
        _viewMain = uiPanel.ui;

        UIObjectFactory.SetPackageItemExtension(UIPackage.GetItemURL(packageName, "Btn_LevelIcon"), typeof(UI_Btn_LevelIcon));
        m_TopNav = _viewMain.GetChild("TopNav").asCom;

        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        //_backBtn.position = GameUtility_FGUI.Offset_iPhoneX(_backBtn.position, 74);
        _backBtn.onClick.Add(() => { GoToLevelPage(); });

        // 获取当前分类的关卡总数
        _level_count = int.Parse(app_config_data[current_category]["level_count"].ToString());
        Debug.Log($"level_count : {_level_count}");

        // 获取每页显示的关卡数量
        _per_page_count = int.Parse(app_config_data["Levels_List"]["per_page_count"].ToString());
        Debug.Log($"per_page_count : {_per_page_count}");

        // 计算总页数
        _total_page_count = (int)Mathf.Ceil((float)_level_count / _per_page_count);
        Debug.Log($"total_page_count : {_total_page_count}");

        // 从全局变量中读取当前页码
        _current_page_no = FD1_GlobalVariable.current_page_no;

        // 确保页码在有效范围内
        if (_current_page_no < 1 || _current_page_no > _total_page_count)
        {
            _current_page_no = 1;
            FD1_GlobalVariable.current_page_no = 1;
        }

        // 初始化关卡列表
        _list = _viewMain.GetChild("List_Level").asList;
        _list.itemRenderer = RenderListItem;
        RefreshLevelList();

        // 初始化分页控制
        _pageControl = _viewMain.GetChild("Nav_Page_Control").asCom;
        UpdatePageControl();
        _pageControl.GetChild("Btn_Nav_Left").asButton.onClick.Add(() => { GoToPrevPage(); });
        _pageControl.GetChild("Btn_Nav_Right").asButton.onClick.Add(() => { GoToNextPage(); });
        _pageControl.GetChild("Btn_Nav_Right").scaleX = -1;

        GButton Icon_Category = _viewMain.GetChild("Icon_Category").asButton;

        GLoader icon = Icon_Category.GetChild("icon").asLoader;
        GLoader category_title = Icon_Category.GetChild("image_title").asLoader;

        string title_name = current_category;

        if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.SimplifiedChinese)
        {
            title_name += "-sc";
        }
        else if (GlobalVariable.osLanguage == GlobalVariable.OSLanguage.TraditionalChinese)
        {
            title_name += "-tc";
        }
        else
        {
            title_name += "-en";
        }

        Debug.Log($"title_name : {title_name}");

        GameUtility_FGUI.ChangeSprite(icon, packageName, current_category);
        GameUtility_FGUI.ChangeSprite(category_title, packageName, title_name);
    }

    void RefreshLevelList()
    {
        // 停止所有锁图标动画
        StopAllLockAnimations();

        // 停止所有可用关卡动画
        StopAllAvailableLevelAnimations();

        // 计算当前页的起始和结束关卡索引
        int startIndex = (_current_page_no - 1) * _per_page_count;
        int endIndex = Mathf.Min(startIndex + _per_page_count, _level_count);
        int itemCount = endIndex - startIndex;

        // 更新列表项目数量
        _list.numItems = itemCount;

        Debug.Log($"显示关卡从 {startIndex + 1} 到 {endIndex}，共 {itemCount} 个");
    }

    // 停止所有锁图标动画
    private void StopAllLockAnimations()
    {
        foreach (var anim in _lockAnimations.Values)
        {
            if (anim != null && anim.IsActive())
            {
                anim.Kill();
            }
        }
        _lockAnimations.Clear();
    }

    // 停止单个锁图标动画
    private void StopLockAnimation(GObject lockIcon)
    {
        if (_lockAnimations.ContainsKey(lockIcon))
        {
            if (_lockAnimations[lockIcon] != null && _lockAnimations[lockIcon].IsActive())
            {
                _lockAnimations[lockIcon].Kill();
            }
            _lockAnimations.Remove(lockIcon);
        }
    }

    // 停止所有可用关卡动画
    private void StopAllAvailableLevelAnimations()
    {
        foreach (var anim in _availableLevelAnimations.Values)
        {
            if (anim != null && anim.IsActive())
            {
                anim.Kill();
            }
        }
        _availableLevelAnimations.Clear();
    }

    // 停止单个可用关卡动画
    private void StopAvailableLevelAnimation(GObject levelItem)
    {
        if (_availableLevelAnimations.ContainsKey(levelItem))
        {
            if (_availableLevelAnimations[levelItem] != null && _availableLevelAnimations[levelItem].IsActive())
            {
                _availableLevelAnimations[levelItem].Kill();
            }
            _availableLevelAnimations.Remove(levelItem);
        }
    }

    // 添加锁图标飘动动画
    private void AddLockAnimation(GObject lockIcon)
    {
        if (lockIcon == null || !lockIcon.visible)
            return;

        // 先停止已有动画
        StopLockAnimation(lockIcon);

        // 保存原始位置和旋转
        Vector2 originalPos = lockIcon.position;
        float originalRotation = lockIcon.rotation;

        // 创建浮动+旋转的动画序列
        Sequence sequence = DOTween.Sequence();

        sequence.Append(DOTween.To(() => lockIcon.rotation, r => lockIcon.rotation = r, originalRotation - 20, 0.8f).SetEase(Ease.Linear));
        sequence.Append(DOTween.To(() => lockIcon.rotation, r => lockIcon.rotation = r, originalRotation + 15, 0.8f).SetEase(Ease.Linear));

        // 最后回到原始位置和旋转
        sequence.Append(DOTween.To(() => lockIcon.x, x => lockIcon.x = x, originalPos.x, 0.4f));
        sequence.Join(DOTween.To(() => lockIcon.y, y => lockIcon.y = y, originalPos.y, 0.4f));
        sequence.Join(DOTween.To(() => lockIcon.rotation, r => lockIcon.rotation = r, originalRotation, 0.4f));

        // 设置为无限循环
        sequence.SetLoops(-1, LoopType.Restart);

        // 存储动画引用
        _lockAnimations[lockIcon] = sequence;
    }

    void UpdatePageControl()
    {
        // 更新页码显示
        _pageControl.GetChild("txt_page_no").asTextField.text = $"{_current_page_no}/{_total_page_count}";

        // 根据当前页码启用或禁用导航按钮
        _pageControl.GetChild("Btn_Nav_Left").asButton.visible = (_current_page_no > 1);
        _pageControl.GetChild("Btn_Nav_Right").asButton.visible = (_current_page_no < _total_page_count);
    }

    void GoToPrevPage()
    {
        if (_current_page_no > 1)
        {
            _current_page_no--;
            // 更新全局变量中的页码
            FD1_GlobalVariable.current_page_no = _current_page_no;
            RefreshLevelList();
            UpdatePageControl();

            // 播放按钮点击音效
            PlayTurnPageSound();
        }
    }

    void GoToNextPage()
    {
        if (_current_page_no < _total_page_count)
        {
            _current_page_no++;
            // 更新全局变量中的页码
            FD1_GlobalVariable.current_page_no = _current_page_no;
            RefreshLevelList();
            UpdatePageControl();

            // 播放按钮点击音效
            PlayTurnPageSound();
        }
    }

    void GoToLevelPage()
    {
        PlayClickSound("BtnClick1");
        StartCoroutine(GoToScene("FD1_LevelPage"));
    }

    private void PlayClickSound(string soundName = "BtnClick1")
    {
        AudioClip btnClip = Resources.Load<AudioClip>($"EffectSounds/{soundName}");
        SoundManager.PlaySFX(btnClip, false, 0, 1);
    }

    void PlayTurnPageSound()
    {
        AudioClip btnClip = Resources.Load<AudioClip>("EffectSounds/TurnPage");
        SoundManager.PlaySFX(btnClip, false, 0, 1);
    }

    void RenderListItem(int index, GObject obj)
    {
        // 计算实际关卡编号
        int levelIndex = ((_current_page_no - 1) * _per_page_count) + index;
        int levelNumber = levelIndex + 1; // 从1开始计数显示给用户

        Debug.Log($"渲染关卡项 {index}，实际关卡编号: {levelNumber}");

        UI_Btn_LevelIcon item = (UI_Btn_LevelIcon)obj;
        item.SetPivot(0.5f, 0.5f);

        item.m_star1.visible = false;
        item.m_star2.visible = false;
        item.m_star3.visible = false;

        item.m_green_bg.visible = false;
        item.m_animal_bg.visible = false;
        item.m_icon_lock.visible = true;

        string levelId = $"{current_category}_{levelNumber}";
        int stars = GameProgressManager.Instance.GetLevelStars(levelId);
        Debug.Log($"关卡 {levelId} 的星级: {stars}");

        item.m_title_num.text = levelNumber.ToString();

        // 声明锁定状态变量
        bool isLocked = true;
        bool isLevelPass = GetLockState(levelNumber);
        //isLocked = false;

        if (levelNumber == 1)
        {
            item.m_green_bg.visible = true;

            item.m_animal_bg.visible = false;
            // 第一关始终解锁，不显示锁图标
            item.m_icon_lock.visible = false;

            //GameUtility_FGUI.ChangeSprite(item.m_icon_num, packageName, $"Green_N{levelNumber}");
            item.m_title_num.visible = true;
        }
        else if (levelNumber >= 2 && levelNumber <= 4)
        {

            item.m_animal_bg.visible = false;
            item.m_gray_bg.visible = true;

            //GameUtility_FGUI.ChangeSprite(item.m_icon_num, packageName, $"Green_N{levelNumber}");
            item.m_title_num.visible = true;

            // 根据关卡锁定状态显示锁图标
            item.m_icon_lock.visible = isLocked;
            item.m_title_num.visible = true;

            // 如果关卡锁定，添加锁图标动画
            if (!isLevelPass)
            {
                item.m_green_bg.visible = true;
                item.m_gray_bg.visible = false;
            }
            else
            {
                item.m_green_bg.visible = false;
                item.m_gray_bg.visible = true;
            }
        }
        else
        {
            item.m_green_bg.visible = false;
            item.m_animal_bg.visible = false;

            // 根据关卡锁定状态显示锁图标
            item.m_icon_lock.visible = isLocked;

            // 如果关卡锁定，添加锁图标动画
            if (isLocked)
            {
                //AddLockAnimation(item.m_icon_lock);
            }

            // 实现关卡图标循环显示的逻辑,已经通关的
            if (isLevelPass)
            {
                item.m_title_num.visible = false;
            }
            else
            {
                item.m_title_num.visible = true;
            }

            if (!isLevelPass)
            {
                item.m_green_bg.visible = true;
                item.m_gray_bg.visible = false;
            }
            else
            {
                item.m_green_bg.visible = false;
                item.m_gray_bg.visible = true;
            }

           
        }


        SetLevelIcon(item.m_icon_num, levelNumber, isLevelPass, item.m_animal_bg);

        ShowStars(levelId, item);

        // 添加点击事件，只有未锁定的关卡可以点击
        if (!isLevelPass)
        {
            // 检查关卡是否未玩过，如果是则记录到需要动画的关卡集合中
            if (stars == -1)
            {
                // 将关卡ID添加到需要动画的集合中
                if (!_animatedLevelIds.Contains(levelId))
                {
                    _animatedLevelIds.Add(levelId);
                }

                // 添加动画，但确保只有当前关卡才会显示动画
                AddAvailableLevelAnimation(item, levelId);
            }
        }

        item.onClick.Set(() =>
        {
            // 保存当前选择的关卡和关卡ID
            FD1_GlobalVariable.current_level = levelNumber;
            FD1_GlobalVariable.current_level_id = levelId;

            // 保存当前页码到全局变量
            FD1_GlobalVariable.current_page_no = _current_page_no;

            // 播放点击音效
            PlayClickSound("NextQuestionBegin");

            StartCoroutine(GoToScene(FD1_GlobalVariable.current_category));
            return;
          
            
        });
    }

    // 添加可用关卡轻微摇动动画
    private void AddAvailableLevelAnimation(UI_Btn_LevelIcon levelItem, string levelId)
    {
        if (levelItem == null)
            return;

        // 检查该关卡是否需要显示动画
        if (!_animatedLevelIds.Contains(levelId))
            return;

        // 先停止已有动画
        StopAvailableLevelAnimation(levelItem);

        // 保存原始位置和缩放
        Vector2 originalPos = levelItem.position;
        Vector2 originalScale = new Vector2(levelItem.scaleX, levelItem.scaleY);

        // 创建轻微摇动的动画序列
        Sequence sequence = DOTween.Sequence();

        // 设置动画时间
        float animDuration = 0.5f;
        float returnDuration = 0.3f;
        float delayBetweenLoops = 1f;

        sequence.Append(DOTween.To(() => levelItem.scaleX, x => levelItem.scaleX = x, originalScale.x * 1.12f, animDuration).SetEase(Ease.Linear));
        sequence.Join(DOTween.To(() => levelItem.scaleY, y => levelItem.scaleY = y, originalScale.y * 1.12f, animDuration).SetEase(Ease.Linear));
        sequence.Append(DOTween.To(() => levelItem.scaleX, x => levelItem.scaleX = x, originalScale.x, returnDuration).SetEase(Ease.Linear));
        sequence.Join(DOTween.To(() => levelItem.scaleY, y => levelItem.scaleY = y, originalScale.y, returnDuration).SetEase(Ease.Linear));

        // 添加延迟，使动画不会立即重复
        sequence.AppendInterval(delayBetweenLoops);

        // 设置为无限循环
        sequence.SetLoops(-1, LoopType.Restart);

        // 存储动画引用
        _availableLevelAnimations[levelItem] = sequence;
    }

    private void ShowStars(string levelId, UI_Btn_LevelIcon item)
    {
        int stars = GameProgressManager.Instance.GetLevelStars(levelId);
        Debug.Log($"关卡 {levelId} 的星级: {stars}");
        GObject star1 = item.m_star1;
        GObject star2 = item.m_star2;
        GObject star3 = item.m_star3;

        if (stars == -1)
        {
            star1.visible = false;
            star2.visible = false;
            star3.visible = false;
        }
        else if (stars == 0)
        {
            star1.visible = true;
            star2.visible = true;
            star3.visible = true;

            star1.SetGrayed(true);
            star2.SetGrayed(true);
            star3.SetGrayed(true);
        }
        else if (stars == 1)
        {
            star1.visible = true;
            star2.visible = true;
            star3.visible = true;

            star1.SetGrayed(false);
            star2.SetGrayed(true);
            star3.SetGrayed(true);
        }
        else if (stars == 2)
        {
            star1.visible = true;
            star2.visible = true;
            star3.visible = true;

            star1.SetGrayed(false);
            star2.SetGrayed(false);
            star3.SetGrayed(true);
        }
        else if (stars == 3)
        {
            star1.visible = true;
            star2.visible = true;
            star3.visible = true;

            star1.SetGrayed(false);
            star2.SetGrayed(false);
            star3.SetGrayed(false);
        }
    }

    private bool GetLockState(int levelNumber)
    {
        // 第一关始终解锁
        if (levelNumber == 1)
            return false;

        // 构建关卡ID，格式为"分类_编号"
        string levelId = $"{current_category}_{levelNumber}";

        // 使用GameProgressManager检查关卡是否锁定（未解锁）
        return !GameProgressManager.Instance.IsLevelUnlocked(levelId);
    }

    IEnumerator GoToScene(string go_scene_name)
    {
        yield return new WaitForSeconds(0.3f);

        if (!isGoingNextScene)
        {
            isGoingNextScene = true;

            // 停止所有锁图标动画
            StopAllLockAnimations();

            // 销毁所有事件监听器
            RemoveAllEventListeners();


            yield return new WaitForSeconds(0.5f);
            GameUtility.GoToSceneName_NotAddSuffix(go_scene_name);
        }
    }

    // 销毁所有事件监听器
    private void RemoveAllEventListeners()
    {
        // 移除返回按钮的事件监听
        GObject _backBtn = m_TopNav.GetChild("Btn_Back");
        _backBtn.onClick.Clear();

        // 移除分页控制按钮的事件监听
        if (_pageControl != null)
        {
            _pageControl.GetChild("Btn_Nav_Left").asButton.onClick.Clear();
            _pageControl.GetChild("Btn_Nav_Right").asButton.onClick.Clear();
        }

        // 移除所有列表项的事件监听
        if (_list != null)
        {
            for (int i = 0; i < _list.numChildren; i++)
            {
                GObject obj = _list.GetChildAt(i);
                if (obj is UI_Btn_LevelIcon)
                {
                    (obj as UI_Btn_LevelIcon).onClick.Clear();
                }
            }
        }
    }

    private void SetLevelIcon(GLoader iconLoader, int levelNumber, bool isLevelPass, GImage animalBg)
    {
        // 计算循环后的图标索引
        // 对于第5-8关使用"icon_leve5"到"icon_leve8"
        // 对于第9-12关使用"icon_leve1"到"icon_leve4"
        // 以此类推

        if (levelNumber <= 4)
        {
            // 第1-4关使用"Green_N1"到"Green_N4"
            //GameUtility_FGUI.ChangeSprite(iconLoader, packageName, $"Green_N{levelNumber}");
            iconLoader.visible = false;
        }
        else
        {
            // 确定当前图标组 (0代表第5-8关，1代表第9-12关，依此类推)
            int iconGroup = ((levelNumber - 5) / 4) % 2;

            // 计算在当前组内的索引 (0-3)
            int indexInGroup = (levelNumber - 5) % 4;

            // 根据组和索引确定最终的图标索引
            int iconIndex = iconGroup == 0
                ? indexInGroup + 5  // 第5-8关
                : indexInGroup + 1; // 第9-12关、第17-20关等使用"icon_leve1"到"icon_leve4"

            GameUtility_FGUI.ChangeSprite(iconLoader, packageName, $"icon_leve{iconIndex}");
            iconLoader.visible = true;
            if (!isLevelPass)
            {
                iconLoader.visible = false;
            }

            if (iconLoader.visible)
            {
               animalBg.visible = true;
            }
        }
    }

    private void OnDestroy()
    {
        // 确保在销毁时停止所有动画
        StopAllLockAnimations();
        StopAllAvailableLevelAnimations();
    }

    // 检查是否有新解锁的关卡，并自动翻页到包含新解锁关卡的页面
    private IEnumerator CheckForNewlyUnlockedLevels()
    {
        // 等待一帧，确保列表已完全初始化
        yield return null;

        // 获取当前刚玩过的关卡的ID
        string justPlayedLevelId = FD1_GlobalVariable.current_level_id;
        int justPlayedLevel = FD1_GlobalVariable.current_level;

        // 如果用户刚完成了一个关卡（不是直接进入关卡选择界面）
        if (!string.IsNullOrEmpty(justPlayedLevelId))
        {
            Debug.Log($"检测到刚完成关卡: {justPlayedLevelId}");

            // 查找刚完成关卡之后第一个未玩过但已解锁的关卡
            int nextUnplayedLevel = -1;
            string nextUnplayedLevelId = null;

            // 从当前关卡的下一关开始检查
            for (int i = justPlayedLevel + 1; i <= _level_count; i++)
            {
                string levelId = $"{current_category}_{i}";
                bool isUnlocked = !GetLockState(i);
                int stars = GameProgressManager.Instance.GetLevelStars(levelId);

                // 如果关卡已解锁但从未玩过
                if (isUnlocked && stars == -1)
                {
                    nextUnplayedLevel = i;
                    nextUnplayedLevelId = levelId;
                    break;
                }
            }

            // 如果找到了新解锁且未玩过的关卡
            if (nextUnplayedLevel > 0 && !string.IsNullOrEmpty(nextUnplayedLevelId))
            {
                Debug.Log($"发现新解锁未玩过的关卡: {nextUnplayedLevelId}");

                // 计算新关卡在哪一页
                int pageForNewLevel = Mathf.CeilToInt((float)nextUnplayedLevel / _per_page_count);
                Debug.Log($"新关卡在第 {pageForNewLevel} 页，当前页为 {_current_page_no}");

                // 如果新关卡不在当前页，则自动翻页
                if (pageForNewLevel != _current_page_no)
                {
                    // 更新当前页码
                    _current_page_no = pageForNewLevel;
                    FD1_GlobalVariable.current_page_no = _current_page_no;

                    // 刷新列表和页码控制
                    RefreshLevelList();
                    UpdatePageControl();

                    Debug.Log($"自动翻页到第 {_current_page_no} 页，显示新解锁关卡");
                }

                // 将新关卡ID添加到需要动画的集合中
                _animatedLevelIds.Add(nextUnplayedLevelId);

                // 延迟一帧，确保UI已经更新
                yield return null;

                // 遍历所有列表项，对应新解锁的关卡添加动画
                for (int i = 0; i < _list.numChildren; i++)
                {
                    GObject obj = _list.GetChildAt(i);
                    if (obj is UI_Btn_LevelIcon)
                    {
                        UI_Btn_LevelIcon item = obj as UI_Btn_LevelIcon;

                        // 计算此列表项对应的关卡编号
                        int levelIndex = ((_current_page_no - 1) * _per_page_count) + i;
                        int levelNumber = levelIndex + 1;

                        // 如果是新解锁的关卡，添加动画
                        if (levelNumber == nextUnplayedLevel)
                        {
                            string levelId = $"{current_category}_{levelNumber}";
                            AddAvailableLevelAnimation(item, levelId);
                            break;
                        }
                    }
                }
            }
        }
    }

    // 重置游戏进度（仅用于调试）
    private void ResetGameProgress()
    {
        //GameProgressManager.Instance.ResetProgress();
        //Debug.Log("游戏进度已重置");
    }


}

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!1 &13
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 25}
  - 114: {fileID: 54}
  m_Layer: 0
  m_Name: Center
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &14
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 26}
  - 33: {fileID: 46}
  - 23: {fileID: 38}
  - 114: {fileID: 55}
  m_Layer: 0
  m_Name: Sliced Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &15
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 27}
  - 33: {fileID: 47}
  - 23: {fileID: 39}
  - 114: {fileID: 56}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &16
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 28}
  - 33: {fileID: 48}
  - 23: {fileID: 40}
  - 114: {fileID: 57}
  m_Layer: 0
  m_Name: Sliced Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &17
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 29}
  - 114: {fileID: 58}
  m_Layer: 0
  m_Name: TextBox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &18
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 30}
  - 33: {fileID: 49}
  - 23: {fileID: 41}
  - 114: {fileID: 59}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &19
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 31}
  - 33: {fileID: 50}
  - 23: {fileID: 42}
  - 114: {fileID: 60}
  m_Layer: 0
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &20
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 32}
  - 33: {fileID: 51}
  - 23: {fileID: 43}
  - 114: {fileID: 61}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &21
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 33}
  - 33: {fileID: 52}
  - 23: {fileID: 44}
  - 114: {fileID: 62}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &22
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 34}
  - 33: {fileID: 53}
  - 23: {fileID: 45}
  - 114: {fileID: 63}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &23
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 35}
  - 114: {fileID: 64}
  m_Layer: 0
  m_Name: Lives
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &24
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 36}
  - 20: {fileID: 37}
  - 114: {fileID: 65}
  m_Layer: 0
  m_Name: tk2dCamera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &25
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 512, y: 384, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 31}
  m_Father: {fileID: 36}
--- !u!4 &26
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -158.20697, y: -84, z: 1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &27
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 38, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 29}
--- !u!4 &28
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 28, z: 1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 29}
--- !u!4 &29
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 512, y: 384, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 28}
  - {fileID: 27}
  m_Father: {fileID: 36}
--- !u!4 &30
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -11, y: -12, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &31
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 25}
--- !u!4 &32
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -126, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &33
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -77, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &34
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -31, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &35
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1024, y: 768, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 26}
  - {fileID: 34}
  - {fileID: 33}
  - {fileID: 32}
  - {fileID: 30}
  m_Father: {fileID: 36}
--- !u!4 &36
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 25}
  - {fileID: 35}
  - {fileID: 29}
  m_Father: {fileID: 0}
--- !u!20 &37
Camera:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 20
  field of view: 60
  orthographic: 1
  orthographic size: 480
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &38
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &39
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &40
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &41
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &42
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &43
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &44
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &45
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &46
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Mesh: {fileID: 0}
--- !u!33 &47
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Mesh: {fileID: 0}
--- !u!33 &48
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Mesh: {fileID: 0}
--- !u!33 &49
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Mesh: {fileID: 0}
--- !u!33 &50
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Mesh: {fileID: 0}
--- !u!33 &51
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Mesh: {fileID: 0}
--- !u!33 &52
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Mesh: {fileID: 0}
--- !u!33 &53
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Mesh: {fileID: 0}
--- !u!114 &54
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b223ab045d4e8544b4e8a23f604b903, type: 1}
  m_Name: 
  anchor: -1
  _anchorPoint: 4
  anchorToNativeBounds: 0
  offset: {x: 0, y: 0}
  tk2dCamera: {fileID: 0}
  _anchorCamera: {fileID: 37}
--- !u!114 &55
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 0, g: .657381773, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 156, y: 78}
  _anchor: 0
  _borderOnly: 0
  legacyMode: 0
  borderTop: .200000003
  borderBottom: .200000003
  borderLeft: .177777782
  borderRight: .177777782
  _createBoxCollider: 0
--- !u!114 &56
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: 'Try changing resolution

    tlese elements should display

    at the same relative position.


    Try 480x320 -

    This resolution has an override

    to display scaled by 0.5 rather than

    pixel perfect.'
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 4
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 500
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: 'Try changing resolution

      tlese elements should display

      at the same relative position.


      Try 480x320 -

      This resolution has an override

      to display scaled by 0.5 rather than

      pixel perfect.'
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 4
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 500
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &57
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .795428455, g: .268656731, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 615.538696, y: 241.387726}
  _anchor: 4
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!114 &58
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b223ab045d4e8544b4e8a23f604b903, type: 1}
  m_Name: 
  anchor: -1
  _anchorPoint: 4
  anchorToNativeBounds: 0
  offset: {x: 0, y: 0}
  tk2dCamera: {fileID: 0}
  _anchorCamera: {fileID: 37}
--- !u!114 &59
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: lives
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: lives
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &60
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 640, y: 640, z: 640}
  _spriteId: 4
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &61
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 0
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &62
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 2
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &63
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 1
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &64
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b223ab045d4e8544b4e8a23f604b903, type: 1}
  m_Name: 
  anchor: -1
  _anchorPoint: 8
  anchorToNativeBounds: 0
  offset: {x: 0, y: 0}
  tk2dCamera: {fileID: 0}
  _anchorCamera: {fileID: 37}
--- !u!114 &65
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d60c017246963b14c9806a06670568b5, type: 1}
  m_Name: 
  version: 1
  cameraSettings:
    projection: 0
    orthographicSize: 10
    orthographicPixelsPerMeter: 1
    orthographicOrigin: 0
    orthographicType: 0
    transparencySortMode: 2
    fieldOfView: 60
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
  resolutionOverride:
  - name: 
    matchBy: 0
    width: 480
    height: 320
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: .5
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 0
    fitMode: 0
  inheritSettings: {fileID: 0}
  nativeResolutionWidth: 960
  nativeResolutionHeight: 640
  _unityCamera: {fileID: 37}
  viewportClippingEnabled: 0
  viewportRegion: {x: 0, y: 0, z: 100, w: 100}
  zoomFactor: 1
  forceResolutionInEditor: 1
  forceResolution: {x: 1024, y: 768}
--- !u!196 &66
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}

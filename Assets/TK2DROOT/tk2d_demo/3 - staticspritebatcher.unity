%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!1 &7
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 10}
  - 23: {fileID: 14}
  - 33: {fileID: 16}
  - 114: {fileID: 21}
  m_Layer: 0
  m_Name: Static Sprite Batcher
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &8
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 11}
  - 23: {fileID: 15}
  - 33: {fileID: 17}
  - 114: {fileID: 22}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &9
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 12}
  - 20: {fileID: 13}
  - 92: {fileID: 19}
  - 124: {fileID: 20}
  - 81: {fileID: 18}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &10
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 7}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &11
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 8}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &12
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 9}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!20 &13
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 9}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0
  far clip plane: 20
  field of view: 1
  orthographic: 1
  orthographic size: 1
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &14
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 7}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &15
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 8}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &16
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 7}
  m_Mesh: {fileID: 0}
--- !u!33 &17
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 8}
  m_Mesh: {fileID: 0}
--- !u!81 &18
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 9}
  m_Enabled: 1
--- !u!92 &19
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 9}
  m_Enabled: 1
--- !u!124 &20
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 9}
  m_Enabled: 1
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 7}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d4775e89280fa044835889590a966f0, type: 1}
  m_Name: 
  version: 3
  batchedSprites:
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -.23033759, y: 0, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -.23033759
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: .400912434, y: -.257774353, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: .400912434
      e10: 0
      e11: 1
      e12: 0
      e13: -.257774353
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.03216243, y: -.381057978, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: 1.03216243
      e10: 0
      e11: 1
      e12: 0
      e13: -.381057978
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: .716537476, y: -.381057978, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: .716537476
      e10: 0
      e11: 1
      e12: 0
      e13: -.381057978
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.34778738, y: -.381057978, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: 1.34778738
      e10: 0
      e11: 1
      e12: 0
      e13: -.381057978
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: .0852874219, y: -.123283386, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: .0852874219
      e10: 0
      e11: 1
      e12: 0
      e13: -.123283386
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock2
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -1.17721248, y: 0, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -1.17721248
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock1
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -1.49283743, y: 0, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -1.49283743
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock3
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -.861587524, y: 0, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -.861587524
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock5
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.66341233, y: -.381057978, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: 1.66341233
      e10: 0
      e11: 1
      e12: 0
      e13: -.381057978
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: rock4
    parentId: -1
    spriteId: 21
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -.545962572, y: 0, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -.545962572
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: chest
    parentId: -1
    spriteId: 20
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.34778738, y: -.246566534, z: -.185115457}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: 1.34778738
      e10: 0
      e11: 1
      e12: 0
      e13: -.246566534
      e20: 0
      e21: 0
      e22: 1
      e23: -.185115457
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  allTextMeshData: []
  spriteCollection: {fileID: 0}
  flags: 1
  _scale: {x: 1, y: 1, z: 1}
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 8}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: Select StaticSpriteBatcher, click Edit
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 1
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 39
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: Select StaticSpriteBatcher, click Edit
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 1
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 39
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!196 &23
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!43 &27
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  serializedVersion: 6
  m_SubMeshes:
  - firstByte: 0
    indexCount: 60
    isTriStrip: 0
    triangleCount: 20
    firstVertex: 0
    vertexCount: 20
    localAABB:
      m_Center: {x: .0312500596, y: 5.96046448e-08, z: 0}
      m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshCompression: 0
  m_IndexBuffer: 0200010000000200030001000400050002000200050003000600070004000400070005000800070006000800090007000a000b00080008000b0009000c000b000a000c000d000b000e000d000c000e000f000d0010000f000e00100011000f00120011001000120013001100000001001200120001001300
  m_Skin: []
  m_BindPose: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 20
    m_Streams[0]:
      channelMask: 1
      offset: 0
      stride: 12
      align: 16
    m_Streams[1]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[2]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[3]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_DataSize: 240
    _typelessdata: f0ffff3c0000203e000080bff0ffff3c0000203e0000803f000020be0100b43f000080bf000020be0100b43f0000803f000020bef0ff7fbd000080bf000020bef0ff7fbd0000803f0000b0bf0000203e000080bf0000b0bf0000203e0000803ff0ffffbcfeff9fbe000080bff0ffffbcfeff9fbe0000803f000010bf0000b4bf000080bf000010bf0000b4bf0000803f0100903effff7fbe000080bf0100903effff7fbe0000803f0000983f000094bf000080bf0000983f000094bf0000803f0000a03e2000003d000080bf0000a03e2000003d0000803f0100b83f0000183f000080bf0100b83f0000183f0000803f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BindPoses:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Colors:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
  m_LocalAABB:
    m_Center: {x: .0312500596, y: 5.96046448e-08, z: 0}
    m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshUsageFlags: 0
  m_MeshOptimized: 0
--- !u!43 &28
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  serializedVersion: 6
  m_SubMeshes:
  - firstByte: 0
    indexCount: 60
    isTriStrip: 0
    triangleCount: 20
    firstVertex: 0
    vertexCount: 20
    localAABB:
      m_Center: {x: -.0312500596, y: 5.96046448e-08, z: 0}
      m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshCompression: 0
  m_IndexBuffer: 00000100020001000300020002000500040003000500020004000700060005000700040006000700080007000900080008000b000a0009000b0008000a000b000c000b000d000c000c000d000e000d000f000e000e000f0010000f0011001000100011001200110013001200120001000000130001001200
  m_Skin: []
  m_BindPose: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 20
    m_Streams[0]:
      channelMask: 1
      offset: 0
      stride: 12
      align: 16
    m_Streams[1]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[2]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[3]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_DataSize: 240
    _typelessdata: f0ffffbc0000203e000080bff0ffffbc0000203e0000803f0000203e0100b43f000080bf0000203e0100b43f0000803f0000203ef0ff7fbd000080bf0000203ef0ff7fbd0000803f0000b03f0000203e000080bf0000b03f0000203e0000803ff0ffff3cfeff9fbe000080bff0ffff3cfeff9fbe0000803f0000103f0000b4bf000080bf0000103f0000b4bf0000803f010090beffff7fbe000080bf010090beffff7fbe0000803f000098bf000094bf000080bf000098bf000094bf0000803f0000a0be2000003d000080bf0000a0be2000003d0000803f0100b8bf0000183f000080bf0100b8bf0000183f0000803f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BindPoses:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Colors:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
  m_LocalAABB:
    m_Center: {x: -.0312500596, y: 5.96046448e-08, z: 0}
    m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshUsageFlags: 0
  m_MeshOptimized: 0
--- !u!43 &29
Mesh:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: 
  serializedVersion: 6
  m_SubMeshes:
  - firstByte: 0
    indexCount: 60
    isTriStrip: 0
    triangleCount: 20
    firstVertex: 0
    vertexCount: 20
    localAABB:
      m_Center: {x: -.0312500596, y: 5.96046448e-08, z: 0}
      m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshCompression: 0
  m_IndexBuffer: 00000100020001000300020002000500040003000500020004000700060005000700040006000700080007000900080008000b000a0009000b0008000a000b000c000b000d000c000c000d000e000d000f000e000e000f0010000f0011001000100011001200110013001200120001000000130001001200
  m_Skin: []
  m_BindPose: []
  m_VertexData:
    m_CurrentChannels: 1
    m_VertexCount: 20
    m_Streams[0]:
      channelMask: 1
      offset: 0
      stride: 12
      align: 16
    m_Streams[1]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[2]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_Streams[3]:
      channelMask: 0
      offset: 0
      stride: 0
      align: 16
    m_DataSize: 240
    _typelessdata: f0ffffbc0000203e000080bff0ffffbc0000203e0000803f0000203e0100b43f000080bf0000203e0100b43f0000803f0000203ef0ff7fbd000080bf0000203ef0ff7fbd0000803f0000b03f0000203e000080bf0000b03f0000203e0000803ff0ffff3cfeff9fbe000080bff0ffff3cfeff9fbe0000803f0000103f0000b4bf000080bf0000103f0000b4bf0000803f010090beffff7fbe000080bf010090beffff7fbe0000803f000098bf000094bf000080bf000098bf000094bf0000803f0000a0be2000003d000080bf0000a0be2000003d0000803f0100b8bf0000183f000080bf0100b8bf0000183f0000803f
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BindPoses:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Colors:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
  m_LocalAABB:
    m_Center: {x: -.0312500596, y: 5.96046448e-08, z: 0}
    m_Extent: {x: 1.40625, y: 1.40625, z: 1}
  m_MeshUsageFlags: 0
  m_MeshOptimized: 0
--- !u!1 &30
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 55}
  - 33: {fileID: 102}
  - 23: {fileID: 81}
  - 114: {fileID: 159}
  - 65: {fileID: 154}
  m_Layer: 0
  m_Name: logo_bg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &31
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 56}
  - 33: {fileID: 103}
  - 23: {fileID: 82}
  - 114: {fileID: 160}
  m_Layer: 0
  m_Name: logo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &32
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 57}
  - 114: {fileID: 161}
  m_Layer: 0
  m_Name: GameController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &33
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 58}
  - 33: {fileID: 104}
  - 23: {fileID: 83}
  - 114: {fileID: 162}
  - 114: {fileID: 163}
  - 82: {fileID: 156}
  m_Layer: 8
  m_Name: Button_Reload
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &34
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 59}
  - 23: {fileID: 84}
  - 33: {fileID: 105}
  - 114: {fileID: 164}
  m_Layer: 8
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &35
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 60}
  m_Layer: 0
  m_Name: Spikes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &36
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 61}
  - 23: {fileID: 85}
  - 33: {fileID: 106}
  - 114: {fileID: 165}
  m_Layer: 0
  m_Name: background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &37
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 62}
  m_Layer: 0
  m_Name: Boxes
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &38
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 63}
  - 33: {fileID: 107}
  - 23: {fileID: 86}
  - 114: {fileID: 166}
  - 54: {fileID: 123}
  - 64: {fileID: 140}
  m_Layer: 0
  m_Name: spike
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &39
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 64}
  - 33: {fileID: 108}
  - 23: {fileID: 87}
  - 114: {fileID: 167}
  - 54: {fileID: 124}
  - 64: {fileID: 139}
  m_Layer: 0
  m_Name: spike
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &40
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 65}
  - 33: {fileID: 109}
  - 23: {fileID: 88}
  - 114: {fileID: 168}
  - 54: {fileID: 125}
  - 64: {fileID: 138}
  m_Layer: 0
  m_Name: spike
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &41
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 66}
  - 33: {fileID: 110}
  - 23: {fileID: 89}
  - 114: {fileID: 169}
  - 54: {fileID: 126}
  - 65: {fileID: 153}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &42
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 67}
  - 33: {fileID: 111}
  - 23: {fileID: 90}
  - 114: {fileID: 170}
  - 54: {fileID: 127}
  - 65: {fileID: 152}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &43
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 68}
  - 33: {fileID: 112}
  - 23: {fileID: 91}
  - 114: {fileID: 171}
  - 54: {fileID: 128}
  - 65: {fileID: 151}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &44
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 69}
  - 33: {fileID: 113}
  - 23: {fileID: 92}
  - 114: {fileID: 172}
  - 54: {fileID: 129}
  - 65: {fileID: 150}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &45
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 70}
  - 33: {fileID: 114}
  - 23: {fileID: 93}
  - 114: {fileID: 173}
  - 54: {fileID: 130}
  - 65: {fileID: 149}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &46
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 71}
  - 33: {fileID: 115}
  - 23: {fileID: 94}
  - 114: {fileID: 174}
  - 54: {fileID: 131}
  - 65: {fileID: 148}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &47
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 72}
  - 33: {fileID: 116}
  - 23: {fileID: 95}
  - 114: {fileID: 175}
  - 54: {fileID: 132}
  - 65: {fileID: 147}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &48
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 73}
  - 33: {fileID: 117}
  - 23: {fileID: 96}
  - 114: {fileID: 176}
  - 54: {fileID: 133}
  - 65: {fileID: 146}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &49
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 74}
  - 33: {fileID: 118}
  - 23: {fileID: 97}
  - 114: {fileID: 177}
  - 54: {fileID: 134}
  - 65: {fileID: 145}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &50
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 75}
  - 23: {fileID: 98}
  - 33: {fileID: 119}
  - 114: {fileID: 178}
  - 64: {fileID: 141}
  m_Layer: 0
  m_Name: foreground_spritebatcher
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &51
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 76}
  - 33: {fileID: 120}
  - 23: {fileID: 99}
  - 114: {fileID: 179}
  - 54: {fileID: 135}
  - 65: {fileID: 144}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &52
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 77}
  - 33: {fileID: 121}
  - 23: {fileID: 100}
  - 114: {fileID: 180}
  - 54: {fileID: 136}
  - 65: {fileID: 143}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &53
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 78}
  - 33: {fileID: 122}
  - 23: {fileID: 101}
  - 114: {fileID: 181}
  - 54: {fileID: 137}
  - 65: {fileID: 142}
  m_Layer: 0
  m_Name: box
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &54
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 79}
  - 20: {fileID: 80}
  - 92: {fileID: 157}
  - 124: {fileID: 158}
  - 81: {fileID: 155}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &55
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 30}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: .373269558}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 56}
--- !u!4 &56
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 31}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -12.5318127, y: -6.56132507, z: -.808203697}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 55}
  m_Father: {fileID: 0}
--- !u!4 &57
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 32}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.34230804, y: 3.29418945, z: 6.03540039}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &58
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -10.3356113, y: 9.44643974, z: -3.26409268}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 59}
  m_Father: {fileID: 0}
--- !u!4 &59
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 34}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -.599061966, z: -.0200004578}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 58}
--- !u!4 &60
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 35}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -.000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 63}
  - {fileID: 65}
  - {fileID: 64}
  m_Father: {fileID: 0}
--- !u!4 &61
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 36}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: .386282921, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &62
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 37}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -.000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 76}
  - {fileID: 73}
  - {fileID: 78}
  - {fileID: 77}
  - {fileID: 71}
  - {fileID: 67}
  - {fileID: 69}
  - {fileID: 70}
  - {fileID: 74}
  - {fileID: 68}
  - {fileID: 72}
  - {fileID: 66}
  m_Father: {fileID: 0}
--- !u!4 &63
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  m_LocalRotation: {x: 0, y: 0, z: -.283686787, w: .958917022}
  m_LocalPosition: {x: -5.41715527, y: -3.22704506, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 60}
--- !u!4 &64
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.99638987, y: 3.19903946, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 60}
--- !u!4 &65
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.18615198, y: 3.19903946, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 60}
--- !u!4 &66
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.0844698, y: -4.55423927, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &67
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .857665539, y: -4.55423927, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &68
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.0844698, y: -.429239273, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &69
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  m_LocalRotation: {x: 0, y: 0, z: .2259534, w: .974138141}
  m_LocalPosition: {x: 2.44376373, y: 7.38363934, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &70
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 10.8412704, y: 9.59877777, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &71
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.0844698, y: -2.49173927, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &72
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .857665539, y: 1.63326073, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &73
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .857665539, y: -.429239273, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &74
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .857665539, y: -2.49173927, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &75
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 50}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &76
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  m_LocalRotation: {x: 0, y: 0, z: .487693906, w: .873014748}
  m_LocalPosition: {x: -4.95994282, y: 4.86485767, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &77
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  m_LocalRotation: {x: 0, y: 0, z: .278637171, w: .960396469}
  m_LocalPosition: {x: -4.35818672, y: 2.21020079, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &78
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -4.35818672, y: -.586036682, z: .000500000024}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 62}
--- !u!4 &79
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 54}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!20 &80
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 54}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 10
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &81
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 30}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &82
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 31}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &83
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &84
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 34}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &85
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 36}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &86
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &87
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &88
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &89
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &90
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &91
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &92
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &93
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &94
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &95
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &96
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &97
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &98
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 50}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &99
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &100
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &101
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &102
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 30}
  m_Mesh: {fileID: 0}
--- !u!33 &103
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 31}
  m_Mesh: {fileID: 0}
--- !u!33 &104
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_Mesh: {fileID: 0}
--- !u!33 &105
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 34}
  m_Mesh: {fileID: 0}
--- !u!33 &106
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 36}
  m_Mesh: {fileID: 0}
--- !u!33 &107
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  m_Mesh: {fileID: 0}
--- !u!33 &108
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  m_Mesh: {fileID: 0}
--- !u!33 &109
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  m_Mesh: {fileID: 0}
--- !u!33 &110
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  m_Mesh: {fileID: 0}
--- !u!33 &111
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  m_Mesh: {fileID: 0}
--- !u!33 &112
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  m_Mesh: {fileID: 0}
--- !u!33 &113
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  m_Mesh: {fileID: 0}
--- !u!33 &114
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  m_Mesh: {fileID: 0}
--- !u!33 &115
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  m_Mesh: {fileID: 0}
--- !u!33 &116
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  m_Mesh: {fileID: 0}
--- !u!33 &117
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  m_Mesh: {fileID: 0}
--- !u!33 &118
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  m_Mesh: {fileID: 0}
--- !u!33 &119
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 50}
  m_Mesh: {fileID: 0}
--- !u!33 &120
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  m_Mesh: {fileID: 0}
--- !u!33 &121
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  m_Mesh: {fileID: 0}
--- !u!33 &122
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  m_Mesh: {fileID: 0}
--- !u!54 &123
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .400000006
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 62
  m_CollisionDetection: 0
--- !u!54 &124
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .400000006
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 62
  m_CollisionDetection: 0
--- !u!54 &125
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .800000012
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 62
  m_CollisionDetection: 0
--- !u!54 &126
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &127
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &128
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &129
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  serializedVersion: 2
  m_Mass: 2
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &130
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &131
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &132
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &133
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &134
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &135
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &136
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!54 &137
Rigidbody:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: .0500000007
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 56
  m_CollisionDetection: 0
--- !u!64 &138
MeshCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_SmoothSphereCollisions: 0
  m_Convex: 0
  m_Mesh: {fileID: 27}
--- !u!64 &139
MeshCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_SmoothSphereCollisions: 0
  m_Convex: 0
  m_Mesh: {fileID: 28}
--- !u!64 &140
MeshCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_SmoothSphereCollisions: 0
  m_Convex: 0
  m_Mesh: {fileID: 29}
--- !u!64 &141
MeshCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 50}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_SmoothSphereCollisions: 0
  m_Convex: 0
  m_Mesh: {fileID: 0}
--- !u!65 &142
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &143
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &144
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &145
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &146
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &147
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &148
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &149
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &150
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.0625, y: 4.125, z: 4}
  m_Center: {x: .0312499702, y: 0, z: 0}
--- !u!65 &151
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &152
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &153
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 2.03125, y: 2.0625, z: 2}
  m_Center: {x: .0156249851, y: 0, z: 0}
--- !u!65 &154
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 30}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 4.4000001, y: 4.4000001, z: -28.2368755}
  m_Center: {x: 0, y: 0, z: -.00550000044}
--- !u!81 &155
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 54}
  m_Enabled: 1
--- !u!82 &156
AudioSource:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_Enabled: 1
  serializedVersion: 3
  m_audioClip: {fileID: 8300000, guid: 50714a94174923d42a7b31cdd432bc6c, type: 1}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    - time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
--- !u!92 &157
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 54}
  m_Enabled: 1
--- !u!124 &158
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 54}
  m_Enabled: 1
--- !u!114 &159
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 30}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 0, g: 0, b: 0, a: 1}
  _scale: {x: 11, y: 11, z: 11}
  _spriteId: 37
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 31}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 37
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &161
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 32}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0ce8aecfcf9c26149b4aee91952682b4, type: 1}
  m_Name: 
--- !u!114 &162
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 0, b: 0, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 32
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &163
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 33}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7eeb7d27f0df004e90cb9fda2d821bd, type: 1}
  m_Name: 
  viewCamera: {fileID: 0}
  buttonDownSprite: button_down
  buttonUpSprite: button_up
  buttonPressedSprite: button_up
  buttonDownSound: {fileID: 0}
  buttonUpSound: {fileID: 0}
  buttonPressedSound: {fileID: 8300000, guid: 50714a94174923d42a7b31cdd432bc6c, type: 1}
  targetObject: {fileID: 32}
  messageName: Reload
  targetScale: 1.10000002
  scaleTime: .0500000007
  pressedWaitTime: .300000012
--- !u!114 &164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 34}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: RELOAD
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 7
  _scale: {x: 10, y: 10, z: 10}
  _kerning: 0
  _maxChars: 6
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: RELOAD
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
    useGradient: 0
    textureGradient: 0
    anchor: 7
    renderLayer: 0
    scale: {x: 10, y: 10, z: 10}
    kerning: 0
    maxChars: 6
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &165
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 36}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d4775e89280fa044835889590a966f0, type: 1}
  m_Name: 
  version: 3
  batchedSprites:
  - type: 1
    name: sky
    parentId: -1
    spriteId: 4
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: 6.45664978}
    localScale: {x: 10, y: 10, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 10
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 6.45664978
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: Sprite
    parentId: -1
    spriteId: 17
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 10.716754, y: -.967757702, z: 3.18804145}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: 10.716754
      e10: 0
      e11: 10
      e12: 0
      e13: -.967757702
      e20: 0
      e21: 0
      e22: 10
      e23: 3.18804145
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: Sprite
    parentId: -1
    spriteId: 18
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -8.87427235, y: 3.20157576, z: 3.18804145}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: -8.87427235
      e10: 0
      e11: 10
      e12: 0
      e13: 3.20157576
      e20: 0
      e21: 0
      e22: 10
      e23: 3.18804145
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  allTextMeshData: []
  spriteCollection: {fileID: 0}
  flags: 1
  _scale: {x: 1, y: 1, z: 1}
--- !u!114 &166
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 38}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: -10, y: 10, z: 10}
  _spriteId: 34
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 140}
  meshColliderPositions:
  - {x: -.0312499702, y: .15625, z: -1}
  - {x: -.0312499702, y: .15625, z: 1}
  - {x: .15625, y: 1.40625012, z: -1}
  - {x: .15625, y: 1.40625012, z: 1}
  - {x: .15625, y: -.0624999404, z: -1}
  - {x: .15625, y: -.0624999404, z: 1}
  - {x: 1.375, y: .15625, z: -1}
  - {x: 1.375, y: .15625, z: 1}
  - {x: .0312499702, y: -.31249994, z: -1}
  - {x: .0312499702, y: -.31249994, z: 1}
  - {x: .5625, y: -1.40625, z: -1}
  - {x: .5625, y: -1.40625, z: 1}
  - {x: -.28125003, y: -.249999985, z: -1}
  - {x: -.28125003, y: -.249999985, z: 1}
  - {x: -1.1875, y: -1.15625, z: -1}
  - {x: -1.1875, y: -1.15625, z: 1}
  - {x: -.3125, y: .0312501192, z: -1}
  - {x: -.3125, y: .0312501192, z: 1}
  - {x: -1.43750012, y: .59375, z: -1}
  - {x: -1.43750012, y: .59375, z: 1}
  meshColliderMesh: {fileID: 29}
  renderLayer: 0
--- !u!114 &167
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 39}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: -10, y: 10, z: 10}
  _spriteId: 34
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 139}
  meshColliderPositions:
  - {x: -.0312499702, y: .15625, z: -1}
  - {x: -.0312499702, y: .15625, z: 1}
  - {x: .15625, y: 1.40625012, z: -1}
  - {x: .15625, y: 1.40625012, z: 1}
  - {x: .15625, y: -.0624999404, z: -1}
  - {x: .15625, y: -.0624999404, z: 1}
  - {x: 1.375, y: .15625, z: -1}
  - {x: 1.375, y: .15625, z: 1}
  - {x: .0312499702, y: -.31249994, z: -1}
  - {x: .0312499702, y: -.31249994, z: 1}
  - {x: .5625, y: -1.40625, z: -1}
  - {x: .5625, y: -1.40625, z: 1}
  - {x: -.28125003, y: -.249999985, z: -1}
  - {x: -.28125003, y: -.249999985, z: 1}
  - {x: -1.1875, y: -1.15625, z: -1}
  - {x: -1.1875, y: -1.15625, z: 1}
  - {x: -.3125, y: .0312501192, z: -1}
  - {x: -.3125, y: .0312501192, z: 1}
  - {x: -1.43750012, y: .59375, z: -1}
  - {x: -1.43750012, y: .59375, z: 1}
  meshColliderMesh: {fileID: 28}
  renderLayer: 0
--- !u!114 &168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 40}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 34
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 138}
  meshColliderPositions:
  - {x: .0312499702, y: .15625, z: -1}
  - {x: .0312499702, y: .15625, z: 1}
  - {x: -.15625, y: 1.40625012, z: -1}
  - {x: -.15625, y: 1.40625012, z: 1}
  - {x: -.15625, y: -.0624999404, z: -1}
  - {x: -.15625, y: -.0624999404, z: 1}
  - {x: -1.375, y: .15625, z: -1}
  - {x: -1.375, y: .15625, z: 1}
  - {x: -.0312499702, y: -.31249994, z: -1}
  - {x: -.0312499702, y: -.31249994, z: 1}
  - {x: -.5625, y: -1.40625, z: -1}
  - {x: -.5625, y: -1.40625, z: 1}
  - {x: .28125003, y: -.249999985, z: -1}
  - {x: .28125003, y: -.249999985, z: 1}
  - {x: 1.1875, y: -1.15625, z: -1}
  - {x: 1.1875, y: -1.15625, z: 1}
  - {x: .3125, y: .0312501192, z: -1}
  - {x: .3125, y: .0312501192, z: 1}
  - {x: 1.43750012, y: .59375, z: -1}
  - {x: 1.43750012, y: .59375, z: 1}
  meshColliderMesh: {fileID: 27}
  renderLayer: 0
--- !u!114 &169
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 41}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 153}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &170
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 42}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 152}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &171
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 43}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 151}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &172
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 44}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: .470149279, b: .470149279, a: 1}
  _scale: {x: 20, y: 20, z: 20}
  _spriteId: 23
  boxCollider: {fileID: 150}
  meshCollider: {fileID: 0}
  meshColliderPositions:
  - {x: 2.65625, y: .25000006, z: -2}
  - {x: 2.65625, y: .25000006, z: 2}
  - {x: 2.40625024, y: .625, z: -2}
  - {x: 2.40625024, y: .625, z: 2}
  - {x: 1.65625036, y: .25000006, z: -2}
  - {x: 1.65625036, y: .25000006, z: 2}
  - {x: .593750179, y: .25000006, z: -2}
  - {x: .593750179, y: .25000006, z: 2}
  - {x: .15625, y: .9375, z: -2}
  - {x: .15625, y: .9375, z: 2}
  - {x: -.343749821, y: .25000006, z: -2}
  - {x: -.343749821, y: .25000006, z: 2}
  - {x: -1.65624988, y: .25000006, z: -2}
  - {x: -1.65624988, y: .25000006, z: 2}
  - {x: -2.09375, y: .625, z: -2}
  - {x: -2.09375, y: .625, z: 2}
  - {x: -2.46875, y: .25000006, z: -2}
  - {x: -2.46875, y: .25000006, z: 2}
  - {x: -3.78125, y: .25000006, z: -2}
  - {x: -3.78125, y: .25000006, z: 2}
  - {x: -3.78125, y: -.9375, z: -2}
  - {x: -3.78125, y: -.9375, z: 2}
  - {x: 3.78125, y: -.9375, z: -2}
  - {x: 3.78125, y: -.9375, z: 2}
  - {x: 3.78125, y: .25000006, z: -2}
  - {x: 3.78125, y: .25000006, z: 2}
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &173
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 45}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 149}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &174
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 46}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 148}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 47}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 147}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &176
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 48}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .544776082, g: .454027444, b: .203274652, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 146}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &177
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 49}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 145}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 50}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d4775e89280fa044835889590a966f0, type: 1}
  m_Name: 
  version: 3
  batchedSprites:
  - type: 1
    name: platform
    parentId: -1
    spriteId: 35
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: -.0184197798, w: .999830365}
    position: {x: -3.23581934, y: -5.28654337, z: 0}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 9.99321365
      e01: .368333101
      e02: 0
      e03: -3.23581934
      e10: -.368333101
      e11: 9.99321365
      e12: -0
      e13: -5.28654337
      e20: -0
      e21: 0
      e22: 10
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: platform
    parentId: -1
    spriteId: 36
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: .207548663, w: .978224695}
    position: {x: 10.8913431, y: 7.38047791, z: 0}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 9.13847065
      e01: -4.06058455
      e02: 0
      e03: 10.8913431
      e10: 4.06058455
      e11: 9.13847065
      e12: 0
      e13: 7.38047791
      e20: 0
      e21: 0
      e22: 10
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: platform
    parentId: -1
    spriteId: 36
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: -.180207655, w: .983628631}
    position: {x: 10.133338, y: -1.55395436, z: 0}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 9.35050392
      e01: 3.54514813
      e02: 0
      e03: 10.133338
      e10: -3.54514813
      e11: 9.35050392
      e12: -0
      e13: -1.55395436
      e20: -0
      e21: 0
      e22: 10
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: ground
    parentId: -1
    spriteId: 33
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.7949295, y: -8.09610271, z: -.0979156494}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: 1.7949295
      e10: 0
      e11: 10
      e12: 0
      e13: -8.09610271
      e20: 0
      e21: 0
      e22: 10
      e23: -.0979156494
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: ground
    parentId: -1
    spriteId: 33
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -12.0999336, y: -8.2075901, z: -.142457485}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: -12.0999336
      e10: 0
      e11: 10
      e12: 0
      e13: -8.2075901
      e20: 0
      e21: 0
      e22: 10
      e23: -.142457485
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: ground
    parentId: -1
    spriteId: 33
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 15.5219841, y: -7.99175072, z: -.209915638}
    localScale: {x: 10, y: 10, z: 10}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 10
      e01: 0
      e02: 0
      e03: 15.5219841
      e10: 0
      e11: 10
      e12: 0
      e13: -7.99175072
      e20: 0
      e21: 0
      e22: 10
      e23: -.209915638
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  allTextMeshData: []
  spriteCollection: {fileID: 0}
  flags: 1
  _scale: {x: 1, y: 1, z: 1}
--- !u!114 &179
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 51}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 144}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &180
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 52}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: .559701502, g: .470954597, b: .304912001, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 143}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &181
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 53}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 10, y: 10, z: 10}
  _spriteId: 23
  boxCollider: {fileID: 142}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!196 &182
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: GradientFontdata
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 023bfc31841d2f24ba7eb00462467382, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 2
  lineHeight: 0.14687501
  chars:
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: 0, y: 0, z: 0}
    p1: {x: 0, y: 0, z: 0}
    uv0: {x: 0, y: 0, z: 0}
    uv1: {x: 0, y: 0, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    - {x: 0, y: 0}
    advance: 0
    channel: 0
  - p0: {x: -0.00625, y: 0.028125001, z: 0}
    p1: {x: 0.009375, y: 0.012500001, z: 0}
    uv0: {x: 0.9765625, y: 0.45703125, z: 0}
    uv1: {x: 0.99609375, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.03125, y: 0.015624998, z: 0}
    uv0: {x: 0.953125, y: 0.71484375, z: 0}
    uv1: {x: 0.99609375, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.053125, y: 0.075, z: 0}
    uv0: {x: 0.2890625, y: 0.0859375, z: 0}
    uv1: {x: 0.359375, y: 0.0234375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.00625, y: 0.115625, z: 0}
    p1: {x: 0.09375, y: 0.015625, z: 0}
    uv0: {x: 0.51171875, y: 0.43359375, z: 0}
    uv1: {x: 0.63671875, y: 0.30859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0875
    channel: 0
  - p0: {x: 0, y: 0.125, z: 0}
    p1: {x: 0.075, y: 0.015624998, z: 0}
    uv0: {x: 0.51953125, y: 0.85546875, z: 0}
    uv1: {x: 0.61328125, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.071875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.0875, y: 0.015624998, z: 0}
    uv0: {x: 0.09375, y: 0.85546875, z: 0}
    uv1: {x: 0.20703125, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.081250004
    channel: 0
  - p0: {x: -0.003125, y: 0.112500004, z: 0}
    p1: {x: 0.081250004, y: 0.012500003, z: 0}
    uv0: {x: 0.640625, y: 0.43359375, z: 0}
    uv1: {x: 0.74609375, y: 0.30859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.075
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.028125001, y: 0.075, z: 0}
    uv0: {x: 0.36328125, y: 0.08984375, z: 0}
    uv1: {x: 0.40234375, y: 0.02734375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.04375, y: 0.015624998, z: 0}
    uv0: {x: 0.6015625, y: 0.57421875, z: 0}
    uv1: {x: 0.6640625, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.046875, y: 0.015624998, z: 0}
    uv0: {x: 0.53515625, y: 0.57421875, z: 0}
    uv1: {x: 0.59765625, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.00625, y: 0.121875, z: 0}
    p1: {x: 0.053125, y: 0.0625, z: 0}
    uv0: {x: 0.2109375, y: 0.0859375, z: 0}
    uv1: {x: 0.28515625, y: 0.01171875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.00625, y: 0.1, z: 0}
    p1: {x: 0.0625, y: 0.028125001, z: 0}
    uv0: {x: 0.82421875, y: 0.21875, z: 0}
    uv1: {x: 0.91015625, y: 0.12890625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.059375
    channel: 0
  - p0: {x: -0.003125, y: 0.046875, z: 0}
    p1: {x: 0.028125001, y: -6.9849193e-10, z: 0}
    uv0: {x: 0.51953125, y: 0.08984375, z: 0}
    uv1: {x: 0.55859375, y: 0.03125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.021875
    channel: 0
  - p0: {x: -0.003125, y: 0.078125, z: 0}
    p1: {x: 0.071875, y: 0.040625, z: 0}
    uv0: {x: 0.65625, y: 0.09765625, z: 0}
    uv1: {x: 0.75, y: 0.05078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: 0, y: 0.046875, z: 0}
    p1: {x: 0.03125, y: 0.015625, z: 0}
    uv0: {x: 0.81640625, y: 0.1015625, z: 0}
    uv1: {x: 0.85546875, y: 0.0625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.059375, y: 0.015624998, z: 0}
    uv0: {x: 0.36328125, y: 0.57421875, z: 0}
    uv1: {x: 0.4453125, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.053125
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.09765625, y: 0.71484375, z: 0}
    uv1: {x: 0.19140625, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.0625, y: 0.015624998, z: 0}
    uv0: {x: 0.27734375, y: 0.57421875, z: 0}
    uv1: {x: 0.359375, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.059375
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.1953125, y: 0.71484375, z: 0}
    uv1: {x: 0.2890625, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.29296875, y: 0.71484375, z: 0}
    uv1: {x: 0.38671875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.00625, y: 0.121875, z: 0}
    p1: {x: 0.06875, y: 0.015625002, z: 0}
    uv0: {x: 0.85546875, y: 0.57421875, z: 0}
    uv1: {x: 0.94921875, y: 0.44140625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.121875, z: 0}
    p1: {x: 0.06875, y: 0.015625002, z: 0}
    uv0: {x: 0.19140625, y: 0.43359375, z: 0}
    uv1: {x: 0.28125, y: 0.30078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.71484375, y: 0.85546875, z: 0}
    uv1: {x: 0.80859375, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.012499998, z: 0}
    uv0: {x: 0.33203125, y: 1, z: 0}
    uv1: {x: 0.42578125, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: 0, y: 0.125, z: 0}
    p1: {x: 0.075, y: 0.012499998, z: 0}
    uv0: {x: 0.4296875, y: 1, z: 0}
    uv1: {x: 0.5234375, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.071875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.012499998, z: 0}
    uv0: {x: 0.52734375, y: 1, z: 0}
    uv1: {x: 0.62109375, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: 0, y: 0.090625, z: 0}
    p1: {x: 0.03125, y: 0.015625002, z: 0}
    uv0: {x: 0.78125, y: 0.19921875, z: 0}
    uv1: {x: 0.8203125, y: 0.10546875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.003125, y: 0.090625, z: 0}
    p1: {x: 0.028125001, y: 0.0000000016298145, z: 0}
    uv0: {x: 0.953125, y: 0.57421875, z: 0}
    uv1: {x: 0.9921875, y: 0.4609375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.021875
    channel: 0
  - p0: {x: -0.00625, y: 0.1, z: 0}
    p1: {x: 0.053125, y: 0.015625, z: 0}
    uv0: {x: 0.828125, y: 0.43359375, z: 0}
    uv1: {x: 0.90234375, y: 0.328125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.090625, z: 0}
    p1: {x: 0.0625, y: 0.031250004, z: 0}
    uv0: {x: 0.9140625, y: 0.21875, z: 0}
    uv1: {x: 0.99609375, y: 0.14453125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.059375
    channel: 0
  - p0: {x: -0.003125, y: 0.1, z: 0}
    p1: {x: 0.056250002, y: 0.015625, z: 0}
    uv0: {x: 0.75, y: 0.43359375, z: 0}
    uv1: {x: 0.82421875, y: 0.328125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.065625004, y: 0.015624998, z: 0}
    uv0: {x: 0, y: 0.5625, z: 0}
    uv1: {x: 0.08984375, y: 0.42578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.059375
    channel: 0
  - p0: {x: 0, y: 0.121875, z: 0}
    p1: {x: 0.081250004, y: 0.012500001, z: 0}
    uv0: {x: 0.31640625, y: 0.85546875, z: 0}
    uv1: {x: 0.41796875, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.075
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.012499998, z: 0}
    uv0: {x: 0.234375, y: 1, z: 0}
    uv1: {x: 0.328125, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.012499998, z: 0}
    uv0: {x: 0, y: 0.84765625, z: 0}
    uv1: {x: 0.08984375, y: 0.70703125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.012499998, z: 0}
    uv0: {x: 0.81640625, y: 1, z: 0}
    uv1: {x: 0.90625, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: 0, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.578125, y: 0.71484375, z: 0}
    uv1: {x: 0.66796875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: 0, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.91015625, y: 0.85546875, z: 0}
    uv1: {x: 0.99609375, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.121875, z: 0}
    p1: {x: 0.071875, y: 0.012500001, z: 0}
    uv0: {x: 0.8125, y: 0.85546875, z: 0}
    uv1: {x: 0.90625, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.012499998, z: 0}
    uv0: {x: 0.13671875, y: 1, z: 0}
    uv1: {x: 0.23046875, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.6171875, y: 0.85546875, z: 0}
    uv1: {x: 0.7109375, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.0375, y: 0.015624998, z: 0}
    uv0: {x: 0.80078125, y: 0.57421875, z: 0}
    uv1: {x: 0.8515625, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.034375
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.065625004, y: 0.012499998, z: 0}
    uv0: {x: 0.72265625, y: 1, z: 0}
    uv1: {x: 0.8125, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.390625, y: 0.71484375, z: 0}
    uv1: {x: 0.48046875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.065625004, y: 0.015624998, z: 0}
    uv0: {x: 0.09375, y: 0.5625, z: 0}
    uv1: {x: 0.18359375, y: 0.42578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: 0, y: 0.121875, z: 0}
    p1: {x: 0.065625004, y: 0.015625002, z: 0}
    uv0: {x: 0.28515625, y: 0.43359375, z: 0}
    uv1: {x: 0.3671875, y: 0.30078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.012499998, z: 0}
    uv0: {x: 0.625, y: 1, z: 0}
    uv1: {x: 0.71875, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.065625004, y: 0.012499998, z: 0}
    uv0: {x: 0.91015625, y: 1, z: 0}
    uv1: {x: 0.99609375, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: 0, y: 0.125, z: 0}
    p1: {x: 0.075, y: 0.015624998, z: 0}
    uv0: {x: 0, y: 0.703125, z: 0}
    uv1: {x: 0.09375, y: 0.56640625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: 0, y: 0.121875, z: 0}
    p1: {x: 0.071875, y: 0.012500001, z: 0}
    uv0: {x: 0.859375, y: 0.71484375, z: 0}
    uv1: {x: 0.94921875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.078125, y: 0.015624998, z: 0}
    uv0: {x: 0.2109375, y: 0.85546875, z: 0}
    uv1: {x: 0.3125, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.071875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.012499998, z: 0}
    uv0: {x: 0.0390625, y: 1, z: 0}
    uv1: {x: 0.1328125, y: 0.859375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.071875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.671875, y: 0.71484375, z: 0}
    uv1: {x: 0.76171875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: 0, y: 0.121875, z: 0}
    p1: {x: 0.06875, y: 0.012500001, z: 0}
    uv0: {x: 0.1875, y: 0.57421875, z: 0}
    uv1: {x: 0.2734375, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.065625004
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.484375, y: 0.71484375, z: 0}
    uv1: {x: 0.57421875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.06875, y: 0.015624998, z: 0}
    uv0: {x: 0.765625, y: 0.71484375, z: 0}
    uv1: {x: 0.85546875, y: 0.578125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.071875, y: 0.015624998, z: 0}
    uv0: {x: 0.421875, y: 0.85546875, z: 0}
    uv1: {x: 0.515625, y: 0.71875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.121875, z: 0}
    p1: {x: 0.06875, y: 0.015625002, z: 0}
    uv0: {x: 0.09765625, y: 0.421875, z: 0}
    uv1: {x: 0.1875, y: 0.2890625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.0625
    channel: 0
  - p0: {x: -0.003125, y: 0.121875, z: 0}
    p1: {x: 0.071875, y: 0.015625002, z: 0}
    uv0: {x: 0, y: 0.421875, z: 0}
    uv1: {x: 0.09375, y: 0.2890625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.06875
    channel: 0
  - p0: {x: -0.003125, y: 0.125, z: 0}
    p1: {x: 0.046875, y: 0.015624998, z: 0}
    uv0: {x: 0.66796875, y: 0.57421875, z: 0}
    uv1: {x: 0.73046875, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.059375, y: 0.015624998, z: 0}
    uv0: {x: 0.44921875, y: 0.57421875, z: 0}
    uv1: {x: 0.53125, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.053125
    channel: 0
  - p0: {x: -0.00625, y: 0.125, z: 0}
    p1: {x: 0.04375, y: 0.015624998, z: 0}
    uv0: {x: 0.734375, y: 0.57421875, z: 0}
    uv1: {x: 0.796875, y: 0.4375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.00625, y: 0.121875, z: 0}
    p1: {x: 0.065625004, y: 0.078125, z: 0}
    uv0: {x: 0.5625, y: 0.08984375, z: 0}
    uv1: {x: 0.65234375, y: 0.03515625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.059375
    channel: 0
  - p0: {x: -0.003125, y: 0.021875, z: 0}
    p1: {x: 0.0875, y: -0.0062500006, z: 0}
    uv0: {x: 0.859375, y: 0.125, z: 0}
    uv1: {x: 0.97265625, y: 0.08984375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.084375
    channel: 0
  - p0: {x: -0.00625, y: 0.121875, z: 0}
    p1: {x: 0.040625002, y: 0.084375, z: 0}
    uv0: {x: 0.75390625, y: 0.09765625, z: 0}
    uv1: {x: 0.8125, y: 0.05078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.034375
    channel: 0
  - p0: {x: -0.00625, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.015625004, z: 0}
    uv0: {x: 0.515625, y: 0.3046875, z: 0}
    uv1: {x: 0.5859375, y: 0.203125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.015625004, z: 0}
    uv0: {x: 0.28125, y: 0.19140625, z: 0}
    uv1: {x: 0.34765625, y: 0.08984375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.012500003, z: 0}
    uv0: {x: 0.140625, y: 0.28515625, z: 0}
    uv1: {x: 0.20703125, y: 0.1796875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: 0, y: 0.096875004, z: 0}
    p1: {x: 0.053125, y: 0.015625004, z: 0}
    uv0: {x: 0.88671875, y: 0.32421875, z: 0}
    uv1: {x: 0.953125, y: 0.22265625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.05, y: 0.015624999, z: 0}
    uv0: {x: 0.57421875, y: 0.19921875, z: 0}
    uv1: {x: 0.640625, y: 0.1015625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.012499999, z: 0}
    uv0: {x: 0.3671875, y: 0.296875, z: 0}
    uv1: {x: 0.4375, y: 0.1953125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.012500003, z: 0}
    uv0: {x: 0.90625, y: 0.4375, z: 0}
    uv1: {x: 0.97265625, y: 0.33203125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.053125, y: 0.015625004, z: 0}
    uv0: {x: 0.6640625, y: 0.3046875, z: 0}
    uv1: {x: 0.734375, y: 0.203125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: 0, y: 0.096875004, z: 0}
    p1: {x: 0.03125, y: 0.015625004, z: 0}
    uv0: {x: 0.95703125, y: 0.328125, z: 0}
    uv1: {x: 0.99609375, y: 0.2265625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.00625, y: 0.096875004, z: 0}
    p1: {x: 0.046875, y: 0.012500003, z: 0}
    uv0: {x: 0, y: 0.28515625, z: 0}
    uv1: {x: 0.06640625, y: 0.1796875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.015624999, z: 0}
    uv0: {x: 0.3515625, y: 0.19140625, z: 0}
    uv1: {x: 0.421875, y: 0.09375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.053125, y: 0.015625004, z: 0}
    uv0: {x: 0.8125, y: 0.32421875, z: 0}
    uv1: {x: 0.8828125, y: 0.22265625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: 0, y: 0.09375, z: 0}
    p1: {x: 0.05, y: 0.015624999, z: 0}
    uv0: {x: 0.71484375, y: 0.19921875, z: 0}
    uv1: {x: 0.77734375, y: 0.1015625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.053125, y: 0.015625004, z: 0}
    uv0: {x: 0.29296875, y: 0.296875, z: 0}
    uv1: {x: 0.36328125, y: 0.1953125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.012500003, z: 0}
    uv0: {x: 0.0703125, y: 0.28515625, z: 0}
    uv1: {x: 0.13671875, y: 0.1796875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: 0, y: 0.096875004, z: 0}
    p1: {x: 0.056250002, y: 0.015625004, z: 0}
    uv0: {x: 0.44140625, y: 0.296875, z: 0}
    uv1: {x: 0.51171875, y: 0.1953125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.05, y: 0.012499999, z: 0}
    uv0: {x: 0, y: 0.17578125, z: 0}
    uv1: {x: 0.06640625, y: 0.07421875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.059375, y: 0.015625004, z: 0}
    uv0: {x: 0.2109375, y: 0.296875, z: 0}
    uv1: {x: 0.2890625, y: 0.1953125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.012499999, z: 0}
    uv0: {x: 0.73828125, y: 0.3046875, z: 0}
    uv1: {x: 0.80859375, y: 0.203125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.015625004, z: 0}
    uv0: {x: 0.0703125, y: 0.17578125, z: 0}
    uv1: {x: 0.13671875, y: 0.07421875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: 0, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.012499999, z: 0}
    uv0: {x: 0.140625, y: 0.17578125, z: 0}
    uv1: {x: 0.20703125, y: 0.07421875, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.05, y: 0.015625004, z: 0}
    uv0: {x: 0.2109375, y: 0.19140625, z: 0}
    uv1: {x: 0.27734375, y: 0.08984375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.05, y: 0.015624999, z: 0}
    uv0: {x: 0.64453125, y: 0.19921875, z: 0}
    uv1: {x: 0.7109375, y: 0.1015625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.096875004, z: 0}
    p1: {x: 0.053125, y: 0.015625004, z: 0}
    uv0: {x: 0.58984375, y: 0.3046875, z: 0}
    uv1: {x: 0.66015625, y: 0.203125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.015624999, z: 0}
    uv0: {x: 0.42578125, y: 0.19140625, z: 0}
    uv1: {x: 0.49609375, y: 0.09375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.046875
    channel: 0
  - p0: {x: -0.003125, y: 0.09375, z: 0}
    p1: {x: 0.053125, y: 0.015624999, z: 0}
    uv0: {x: 0.5, y: 0.19140625, z: 0}
    uv1: {x: 0.5703125, y: 0.09375, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.05
    channel: 0
  - p0: {x: -0.00625, y: 0.121875, z: 0}
    p1: {x: 0.046875, y: 0.015625002, z: 0}
    uv0: {x: 0.44140625, y: 0.43359375, z: 0}
    uv1: {x: 0.5078125, y: 0.30078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.003125, y: 0.128125, z: 0}
    p1: {x: 0.025, y: 0.009374995, z: 0}
    uv0: {x: 0, y: 1, z: 0}
    uv1: {x: 0.03515625, y: 0.8515625, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.025
    channel: 0
  - p0: {x: -0.003125, y: 0.121875, z: 0}
    p1: {x: 0.05, y: 0.015625002, z: 0}
    uv0: {x: 0.37109375, y: 0.43359375, z: 0}
    uv1: {x: 0.4375, y: 0.30078125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.04375
    channel: 0
  - p0: {x: -0.00625, y: 0.078125, z: 0}
    p1: {x: 0.081250004, y: 0.03125, z: 0}
    uv0: {x: 0.40625, y: 0.08984375, z: 0}
    uv1: {x: 0.515625, y: 0.03125, z: 0}
    flipped: 0
    gradientUv:
    - {x: 0, y: 1}
    - {x: 0.125, y: 1}
    - {x: 0, y: 0}
    - {x: 0.125, y: 0}
    advance: 0.078125
    channel: 0
  charDictKeys: 
  charDictValues: []
  fontPlatforms: []
  fontPlatformGUIDs: []
  hasPlatformData: 0
  managedFont: 0
  needMaterialInstance: 0
  isPacked: 0
  premultipliedAlpha: 0
  spriteCollection: {fileID: 0}
  useDictionary: 0
  kerning:
  - c0: 65
    c1: 67
    amount: -0.00625
  - c0: 65
    c1: 71
    amount: -0.00625
  - c0: 65
    c1: 79
    amount: 0.003125
  - c0: 65
    c1: 81
    amount: -0.00625
  - c0: 65
    c1: 84
    amount: -0.0125
  - c0: 65
    c1: 85
    amount: -0.003125
  - c0: 65
    c1: 86
    amount: -0.00625
  - c0: 65
    c1: 87
    amount: -0.009375
  - c0: 65
    c1: 97
    amount: -0.003125
  - c0: 65
    c1: 99
    amount: -0.003125
  - c0: 65
    c1: 100
    amount: -0.003125
  - c0: 65
    c1: 101
    amount: -0.003125
  - c0: 65
    c1: 111
    amount: -0.003125
  - c0: 65
    c1: 112
    amount: -0.003125
  - c0: 65
    c1: 113
    amount: -0.003125
  - c0: 65
    c1: 116
    amount: -0.00625
  - c0: 65
    c1: 117
    amount: -0.00625
  - c0: 65
    c1: 118
    amount: -0.00625
  - c0: 65
    c1: 119
    amount: -0.003125
  - c0: 66
    c1: 65
    amount: -0.003125
  - c0: 67
    c1: 65
    amount: -0.003125
  - c0: 68
    c1: 65
    amount: -0.00625
  - c0: 68
    c1: 86
    amount: -0.00625
  - c0: 68
    c1: 87
    amount: -0.00625
  - c0: 68
    c1: 89
    amount: -0.00625
  - c0: 70
    c1: 65
    amount: -0.009375
  - c0: 70
    c1: 97
    amount: -0.0125
  - c0: 70
    c1: 101
    amount: -0.009375
  - c0: 70
    c1: 105
    amount: -0.003125
  - c0: 70
    c1: 111
    amount: -0.009375
  - c0: 70
    c1: 114
    amount: -0.00625
  - c0: 74
    c1: 65
    amount: -0.00625
  - c0: 74
    c1: 97
    amount: -0.009375
  - c0: 74
    c1: 101
    amount: -0.00625
  - c0: 74
    c1: 111
    amount: -0.00625
  - c0: 74
    c1: 117
    amount: -0.009375
  - c0: 75
    c1: 67
    amount: -0.009375
  - c0: 75
    c1: 79
    amount: -0.009375
  - c0: 75
    c1: 101
    amount: -0.009375
  - c0: 75
    c1: 111
    amount: -0.00625
  - c0: 75
    c1: 117
    amount: -0.009375
  - c0: 75
    c1: 121
    amount: -0.00625
  - c0: 76
    c1: 84
    amount: -0.0125
  - c0: 76
    c1: 86
    amount: -0.009375
  - c0: 76
    c1: 87
    amount: -0.009375
  - c0: 76
    c1: 89
    amount: -0.003125
  - c0: 76
    c1: 121
    amount: -0.003125
  - c0: 78
    c1: 65
    amount: -0.00625
  - c0: 79
    c1: 87
    amount: -0.003125
  - c0: 79
    c1: 88
    amount: -0.00625
  - c0: 79
    c1: 89
    amount: -0.009375
  - c0: 80
    c1: 65
    amount: -0.0125
  - c0: 80
    c1: 97
    amount: -0.009375
  - c0: 80
    c1: 101
    amount: -0.003125
  - c0: 80
    c1: 111
    amount: -0.00625
  - c0: 82
    c1: 79
    amount: -0.003125
  - c0: 82
    c1: 83
    amount: -0.009375
  - c0: 82
    c1: 84
    amount: -0.00625
  - c0: 82
    c1: 85
    amount: -0.00625
  - c0: 82
    c1: 86
    amount: -0.00625
  - c0: 82
    c1: 87
    amount: -0.009375
  - c0: 82
    c1: 121
    amount: -0.00625
  - c0: 84
    c1: 45
    amount: -0.0125
  - c0: 84
    c1: 65
    amount: -0.015625
  - c0: 84
    c1: 79
    amount: -0.00625
  - c0: 84
    c1: 97
    amount: -0.015625
  - c0: 84
    c1: 101
    amount: -0.0125
  - c0: 84
    c1: 104
    amount: -0.0125
  - c0: 84
    c1: 105
    amount: -0.009375
  - c0: 84
    c1: 111
    amount: -0.0125
  - c0: 84
    c1: 114
    amount: -0.009375
  - c0: 84
    c1: 117
    amount: -0.0125
  - c0: 84
    c1: 119
    amount: -0.009375
  - c0: 84
    c1: 121
    amount: -0.009375
  - c0: 85
    c1: 65
    amount: -0.009375
  - c0: 86
    c1: 45
    amount: -0.0125
  - c0: 86
    c1: 65
    amount: -0.0125
  - c0: 86
    c1: 71
    amount: -0.009375
  - c0: 86
    c1: 79
    amount: -0.003125
  - c0: 86
    c1: 97
    amount: -0.015625
  - c0: 86
    c1: 101
    amount: -0.009375
  - c0: 86
    c1: 105
    amount: -0.009375
  - c0: 86
    c1: 111
    amount: -0.0125
  - c0: 86
    c1: 117
    amount: -0.009375
  - c0: 87
    c1: 45
    amount: -0.0125
  - c0: 87
    c1: 65
    amount: -0.0125
  - c0: 87
    c1: 79
    amount: -0.009375
  - c0: 87
    c1: 97
    amount: -0.0125
  - c0: 87
    c1: 101
    amount: -0.009375
  - c0: 87
    c1: 104
    amount: -0.009375
  - c0: 87
    c1: 105
    amount: -0.009375
  - c0: 87
    c1: 111
    amount: -0.009375
  - c0: 87
    c1: 117
    amount: -0.009375
  - c0: 87
    c1: 121
    amount: -0.009375
  - c0: 89
    c1: 45
    amount: -0.01875
  - c0: 89
    c1: 65
    amount: -0.00625
  - c0: 89
    c1: 79
    amount: -0.009375
  - c0: 89
    c1: 83
    amount: -0.009375
  - c0: 89
    c1: 97
    amount: -0.01875
  - c0: 89
    c1: 101
    amount: -0.0125
  - c0: 89
    c1: 105
    amount: -0.009375
  - c0: 89
    c1: 111
    amount: -0.015625
  - c0: 89
    c1: 117
    amount: -0.0125
  - c0: 97
    c1: 99
    amount: -0.009375
  - c0: 97
    c1: 103
    amount: -0.009375
  - c0: 97
    c1: 111
    amount: -0.009375
  - c0: 97
    c1: 113
    amount: -0.009375
  - c0: 97
    c1: 116
    amount: -0.015625
  - c0: 97
    c1: 117
    amount: -0.009375
  - c0: 97
    c1: 118
    amount: -0.015625
  - c0: 97
    c1: 119
    amount: -0.0125
  - c0: 97
    c1: 121
    amount: -0.003125
  - c0: 98
    c1: 97
    amount: -0.00625
  - c0: 98
    c1: 114
    amount: -0.00625
  - c0: 99
    c1: 97
    amount: -0.00625
  - c0: 100
    c1: 97
    amount: -0.009375
  - c0: 100
    c1: 118
    amount: -0.009375
  - c0: 100
    c1: 119
    amount: -0.009375
  - c0: 100
    c1: 121
    amount: -0.009375
  - c0: 102
    c1: 97
    amount: -0.0125
  - c0: 106
    c1: 97
    amount: -0.009375
  - c0: 107
    c1: 99
    amount: -0.009375
  - c0: 107
    c1: 111
    amount: -0.009375
  - c0: 108
    c1: 116
    amount: -0.00625
  - c0: 108
    c1: 118
    amount: -0.00625
  - c0: 108
    c1: 119
    amount: -0.003125
  - c0: 108
    c1: 121
    amount: -0.003125
  - c0: 110
    c1: 97
    amount: -0.00625
  - c0: 111
    c1: 75
    amount: -0.003125
  - c0: 111
    c1: 97
    amount: -0.009375
  - c0: 111
    c1: 105
    amount: -0.003125
  - c0: 111
    c1: 111
    amount: -0.00625
  - c0: 111
    c1: 116
    amount: -0.00625
  - c0: 111
    c1: 118
    amount: -0.003125
  - c0: 111
    c1: 119
    amount: -0.003125
  - c0: 111
    c1: 121
    amount: -0.009375
  - c0: 112
    c1: 97
    amount: -0.00625
  - c0: 114
    c1: 111
    amount: -0.003125
  - c0: 114
    c1: 116
    amount: -0.003125
  - c0: 114
    c1: 117
    amount: -0.009375
  - c0: 114
    c1: 118
    amount: -0.00625
  - c0: 114
    c1: 119
    amount: -0.009375
  - c0: 116
    c1: 97
    amount: -0.00625
  - c0: 116
    c1: 111
    amount: -0.00625
  - c0: 117
    c1: 97
    amount: -0.00625
  - c0: 118
    c1: 97
    amount: -0.009375
  - c0: 118
    c1: 101
    amount: -0.003125
  - c0: 118
    c1: 103
    amount: -0.009375
  - c0: 118
    c1: 111
    amount: -0.003125
  - c0: 119
    c1: 97
    amount: -0.009375
  - c0: 119
    c1: 110
    amount: -0.003125
  - c0: 119
    c1: 111
    amount: -0.009375
  - c0: 121
    c1: 97
    amount: -0.009375
  - c0: 121
    c1: 111
    amount: -0.009375
  - c0: 121
    c1: 115
    amount: -0.003125
  largestWidth: 0.0875
  material: {fileID: 2100000, guid: 4f7adb5944587434eb05c98ae8cf0936, type: 2}
  gradientTexture: {fileID: 2800000, guid: 65f34a6b07c20004f95a5e49e1a794e0, type: 3}
  textureGradients: 1
  gradientCount: 8
  texelSize: {x: 0.003125, y: 0.003125}
  invOrthoSize: 1
  halfTargetHeight: 320
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!1 &15
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 27}
  - 23: {fileID: 40}
  - 33: {fileID: 50}
  - 114: {fileID: 63}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &16
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 28}
  - 23: {fileID: 41}
  - 33: {fileID: 51}
  - 114: {fileID: 64}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &17
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 29}
  - 23: {fileID: 42}
  - 33: {fileID: 52}
  - 114: {fileID: 65}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &18
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 30}
  - 23: {fileID: 43}
  - 33: {fileID: 53}
  - 114: {fileID: 66}
  m_Layer: 0
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &19
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 31}
  - 23: {fileID: 44}
  - 33: {fileID: 54}
  - 114: {fileID: 67}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &20
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 32}
  - 23: {fileID: 45}
  - 33: {fileID: 55}
  - 114: {fileID: 68}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &21
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 33}
  m_Layer: 0
  m_Name: text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &22
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 34}
  - 23: {fileID: 46}
  - 33: {fileID: 56}
  - 114: {fileID: 69}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &23
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 35}
  - 33: {fileID: 57}
  - 23: {fileID: 47}
  - 114: {fileID: 70}
  - 114: {fileID: 75}
  m_Layer: 0
  m_Name: sheep
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &24
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 36}
  - 33: {fileID: 58}
  - 23: {fileID: 48}
  - 114: {fileID: 71}
  - 114: {fileID: 74}
  m_Layer: 0
  m_Name: sheep_shadow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &25
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 37}
  - 33: {fileID: 59}
  - 23: {fileID: 49}
  - 114: {fileID: 72}
  m_Layer: 0
  m_Name: grass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &26
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 38}
  - 20: {fileID: 39}
  - 92: {fileID: 61}
  - 124: {fileID: 62}
  - 81: {fileID: 60}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &27
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &28
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.46878111, y: .876727045, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &29
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.46878111, y: 1, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &30
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &31
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.46357393, y: 1, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &32
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.47396386, y: -1, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &33
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .0030691037, y: .0153566077, z: -.967008233}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 34}
  - {fileID: 32}
  - {fileID: 31}
  - {fileID: 29}
  - {fileID: 28}
  - {fileID: 27}
  m_Father: {fileID: 0}
--- !u!4 &34
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1.41298234, y: -.736574769, z: .0583496094}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 33}
--- !u!4 &35
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .498731017, y: -.534485996, z: -.464752823}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &36
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .498731017, y: -.534485996, z: -.464752823}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &37
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 25}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -1, z: -.299325347}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &38
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 26}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!20 &39
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 26}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 1
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &40
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &41
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &42
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &43
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &44
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &45
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &46
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &47
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &48
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &49
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 25}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &50
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Mesh: {fileID: 0}
--- !u!33 &51
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Mesh: {fileID: 0}
--- !u!33 &52
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Mesh: {fileID: 0}
--- !u!33 &53
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Mesh: {fileID: 0}
--- !u!33 &54
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Mesh: {fileID: 0}
--- !u!33 &55
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Mesh: {fileID: 0}
--- !u!33 &56
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Mesh: {fileID: 0}
--- !u!33 &57
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Mesh: {fileID: 0}
--- !u!33 &58
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Mesh: {fileID: 0}
--- !u!33 &59
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 25}
  m_Mesh: {fileID: 0}
--- !u!81 &60
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 26}
  m_Enabled: 1
--- !u!92 &61
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 26}
  m_Enabled: 1
--- !u!124 &62
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 26}
  m_Enabled: 1
--- !u!114 &63
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: PIXEL PERFECT sprites & text
  _color: {r: 1, g: .734265685, b: 0, a: 1}
  _color2: {r: 1, g: 0, b: 0, a: 1}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 4
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 29
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: PIXEL PERFECT sprites & text
    color: {r: 1, g: .734265685, b: 0, a: 1}
    color2: {r: 1, g: 0, b: 0, a: 1}
    useGradient: 1
    textureGradient: 0
    anchor: 4
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 29
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &64
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: 1 gameobject for background
  _color: {r: .216783285, g: 1, b: 0, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 28
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: 1 gameobject for background
    color: {r: .216783285, g: 1, b: 0, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 1
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 28
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &65
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: STATIC SPRITE BATCHER
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 21
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: STATIC SPRITE BATCHER
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 21
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &66
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d4775e89280fa044835889590a966f0, type: 1}
  m_Name: 
  version: 3
  batchedSprites:
  - type: 1
    name: sky
    parentId: -1
    spriteId: 4
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 0, y: 0, z: .523688078}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: .523688078
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: sun
    parentId: -1
    spriteId: 3
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -1.02435458, y: .551989734, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -1.02435458
      e10: 0
      e11: 1
      e12: 0
      e13: .551989734
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: cloud
    parentId: -1
    spriteId: 17
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -.187768191, y: .145093322, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: -.187768191
      e10: 0
      e11: 1
      e12: 0
      e13: .145093322
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: cloud
    parentId: -1
    spriteId: 17
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: 1.46425772, y: -.0780528188, z: 0}
    localScale: {x: -1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: -1
      e01: 0
      e02: 0
      e03: 1.46425772
      e10: -0
      e11: 1
      e12: 0
      e13: -.0780528188
      e20: -0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: cloud
    parentId: -1
    spriteId: 18
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: .734002411, y: .432435393, z: 0}
    localScale: {x: 1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: 1
      e01: 0
      e02: 0
      e03: .734002411
      e10: 0
      e11: 1
      e12: 0
      e13: .432435393
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  - type: 1
    name: cloud
    parentId: -1
    spriteId: 18
    xRefId: -1
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    rotation: {x: 0, y: 0, z: 0, w: 1}
    position: {x: -1.2649883, y: .432435393, z: -.184223399}
    localScale: {x: -1, y: 1, z: 1}
    color: {r: 1, g: 1, b: 1, a: 1}
    baseScale: {x: 1, y: 1, z: 1}
    renderLayer: 0
    internalData0: {x: 0, y: 0}
    internalData1: {x: 0, y: 0}
    internalData2: {x: 0, y: 0}
    colliderData: {x: 0, y: 1}
    formattedText: 
    flags: 0
    anchor: 0
    relativeMatrix:
      e00: -1
      e01: 0
      e02: 0
      e03: -1.2649883
      e10: -0
      e11: 1
      e12: 0
      e13: .432435393
      e20: -0
      e21: 0
      e22: 1
      e23: -.184223399
      e30: 0
      e31: 0
      e32: 0
      e33: 1
  allTextMeshData: []
  spriteCollection: {fileID: 0}
  flags: 1
  _scale: {x: 1, y: 1, z: 1}
--- !u!114 &67
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: 2 DRAW CALLS
  _color: {r: 1, g: .313432813, b: .313432813, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 12
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: 2 DRAW CALLS
    color: {r: 1, g: .313432813, b: .313432813, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 1
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 12
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &68
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: DICED SPRITE
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 6
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 12
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: DICED SPRITE
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 6
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 12
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &69
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: ANIMATED SPRITE
  _color: {r: .845631838, g: .350746274, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 8
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 15
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: ANIMATED SPRITE
    color: {r: .845631838, g: .350746274, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 1
    textureGradient: 0
    anchor: 8
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 15
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &70
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e0e0c37ef9d7b34292486e754b48bd5, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 14
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _animator: {fileID: 75}
  anim: {fileID: 11400000, guid: 18a39674a9b14f54b97497254e83b349, type: 2}
  clipId: 1
  playAutomatically: 1
  createCollider: 0
--- !u!114 &71
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1e0e0c37ef9d7b34292486e754b48bd5, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 0, g: 0, b: 0, a: .211764708}
  _scale: {x: 1, y: -1, z: 1}
  _spriteId: 14
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _animator: {fileID: 74}
  anim: {fileID: 11400000, guid: 18a39674a9b14f54b97497254e83b349, type: 2}
  clipId: 1
  playAutomatically: 1
  createCollider: 0
--- !u!114 &72
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 25}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 13
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!196 &73
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}
--- !u!114 &74
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a5e54113e5604435b8ae87c7754f2725, type: 1}
  m_Name: 
  library: {fileID: 11400000, guid: 18a39674a9b14f54b97497254e83b349, type: 2}
  defaultClipId: 1
  playAutomatically: 1
--- !u!114 &75
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a5e54113e5604435b8ae87c7754f2725, type: 1}
  m_Name: 
  library: {fileID: 11400000, guid: 18a39674a9b14f54b97497254e83b349, type: 2}
  defaultClipId: 1
  playAutomatically: 1

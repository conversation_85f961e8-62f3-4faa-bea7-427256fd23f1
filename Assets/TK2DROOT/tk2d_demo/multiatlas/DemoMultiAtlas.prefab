%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: DemoMultiAtlas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84c7a4b4ee15890498f818262bca0312, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  textures: []
  textureRefs: []
  spriteSheets:
  - texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    tilesX: 44
    tilesY: 1
    numTiles: 0
    anchor: 4
    pad: 0
    scale: {x: 0, y: 0, z: 0}
    additive: 0
    active: 1
    tileWidth: 72
    tileHeight: 110
    tileMarginX: 0
    tileMarginY: 0
    tileSpacingX: 0
    tileSpacingY: 0
    splitMethod: 0
    version: 1
    colliderType: 0
  fonts: []
  defaults:
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    anchor: 4
    pad: 0
    colliderType: 0
  platforms:
  - name: 
    spriteCollection: {fileID: 0}
  managedSpriteCollection: 0
  linkParent: {fileID: 0}
  loadable: 0
  atlasFormat: 0
  maxTextureSize: 256
  forceTextureSize: 0
  forcedTextureWidth: 1024
  forcedTextureHeight: 1024
  textureCompression: 0
  atlasWidth: 256
  atlasHeight: 256
  forceSquareAtlas: 0
  atlasWastage: 12.219238
  allowMultipleAtlases: 1
  removeDuplicates: 1
  textureParams:
  - name: explosion_44FR/0
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 1
    regionX: 0
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 1
    spriteSheetY: 0
    extractRegion: 1
    regionX: 72
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 1
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 2
    spriteSheetY: 0
    extractRegion: 1
    regionX: 144
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 2
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/3
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 3
    spriteSheetY: 0
    extractRegion: 1
    regionX: 216
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 3
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/4
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 4
    spriteSheetY: 0
    extractRegion: 1
    regionX: 288
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 4
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/5
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 5
    spriteSheetY: 0
    extractRegion: 1
    regionX: 360
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 5
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/6
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 6
    spriteSheetY: 0
    extractRegion: 1
    regionX: 432
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 6
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/7
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 7
    spriteSheetY: 0
    extractRegion: 1
    regionX: 504
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 7
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/8
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 8
    spriteSheetY: 0
    extractRegion: 1
    regionX: 576
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 8
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/9
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 9
    spriteSheetY: 0
    extractRegion: 1
    regionX: 648
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 9
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/10
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 10
    spriteSheetY: 0
    extractRegion: 1
    regionX: 720
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 10
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/11
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 11
    spriteSheetY: 0
    extractRegion: 1
    regionX: 792
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 11
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/12
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 12
    spriteSheetY: 0
    extractRegion: 1
    regionX: 864
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 12
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/13
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 13
    spriteSheetY: 0
    extractRegion: 1
    regionX: 936
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 13
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/14
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 14
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1008
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 14
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/15
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 15
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1080
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 15
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/16
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 16
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1152
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 16
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/17
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 17
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1224
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 17
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/18
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 18
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1296
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 18
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/19
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 19
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1368
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 19
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/20
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 20
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1440
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 20
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/21
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 21
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1512
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 21
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/22
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 22
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1584
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 22
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/23
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 23
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1656
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 23
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/24
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 24
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1728
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 24
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/25
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 25
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1800
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 25
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/26
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 26
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1872
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 26
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/27
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 27
    spriteSheetY: 0
    extractRegion: 1
    regionX: 1944
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 27
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/28
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 28
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2016
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 28
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/29
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 29
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2088
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 29
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/30
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 30
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2160
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 30
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/31
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 31
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2232
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 31
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/32
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 32
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2304
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 32
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/33
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 33
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2376
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 33
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/34
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 34
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2448
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 34
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/35
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 35
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2520
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 35
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/36
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 36
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2592
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 36
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/37
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 37
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2664
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 37
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/38
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 38
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2736
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 38
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/39
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 39
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2808
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 39
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/40
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 40
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2880
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 40
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/41
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 41
    spriteSheetY: 0
    extractRegion: 1
    regionX: 2952
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 41
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/42
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 42
    spriteSheetY: 0
    extractRegion: 1
    regionX: 3024
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 42
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_44FR/43
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f38c1e9651d69654c87ea0956466e36d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 36
    anchorY: 55
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 43
    spriteSheetY: 0
    extractRegion: 1
    regionX: 3096
    regionY: 0
    regionW: 72
    regionH: 110
    regionId: 43
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd, type: 2}
  premultipliedAlpha: 1
  altMaterials: []
  atlasMaterials:
  - {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
  - {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
  - {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
  - {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
  atlasTextures:
  - {fileID: 2800000, guid: cd6fbfbe8894ff04f9643675be26a573, type: 3}
  - {fileID: 2800000, guid: 781c2c74baf34e643bf7fdc51b2276ea, type: 3}
  - {fileID: 2800000, guid: a57fd406b36fc6d44b38c2a82b620ccc, type: 3}
  - {fileID: 2800000, guid: 83ed0f7a30dec5040bc629f357183e4a, type: 3}
  atlasTextureFiles: []
  useTk2dCamera: 0
  targetHeight: 640
  targetOrthoSize: 1
  sizeDef:
    type: 0
    orthoSize: 1
    pixelsPerMeter: 100
    width: 960
    height: 640
  globalScale: 1
  globalTextureRescale: 1
  attachPointTestSprites: []
  pixelPerfectPointSampled: 0
  filterMode: 1
  wrapMode: 1
  userDefinedTextureSettings: 1
  mipmapEnabled: 0
  anisoLevel: 1
  physicsEngine: 0
  physicsDepth: 0.1
  disableTrimming: 0
  disableRotation: 0
  normalGenerationMode: 0
  padAmount: -1
  autoUpdate: 1
  editorDisplayScale: 1
  version: 4
  assetName: 
  linkedSpriteCollections: []
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

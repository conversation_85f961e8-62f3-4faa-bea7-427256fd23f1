%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: data
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d546f34a90531a14eaba82a43b05b86b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 3
  materialIdsValid: 1
  needMaterialInstance: 0
  spriteDefinitions:
  - name: explosion_44FR/0
    boundsData:
    - {x: -0.0078125, y: -0.015624996, z: 0}
    - {x: 0.121875, y: 0.14375001, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.06875, y: -0.0875, z: 0}
    - {x: 0.053125, y: -0.0875, z: 0}
    - {x: -0.06875, y: 0.056250006, z: 0}
    - {x: 0.053125, y: 0.056250006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.78906643, y: 0.007816406}
    - {x: 0.78906643, y: 0.16015235}
    - {x: 0.96874607, y: 0.007816406}
    - {x: 0.96874607, y: 0.16015235}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 0
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/1
    boundsData:
    - {x: -0.0062499996, y: -0.017187504, z: 0}
    - {x: 0.1125, y: 0.13437499, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.0625, y: -0.084375, z: 0}
    - {x: 0.05, y: -0.084375, z: 0}
    - {x: -0.0625, y: 0.049999993, z: 0}
    - {x: 0.05, y: 0.049999993, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.78906643, y: 0.17578515}
    - {x: 0.92968357, y: 0.17578515}
    - {x: 0.78906643, y: 0.3437461}
    - {x: 0.92968357, y: 0.3437461}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 72
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/2
    boundsData:
    - {x: 0, y: -0.043750003, z: 0}
    - {x: 0.18125, y: 0.225, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.090625, y: -0.15625, z: 0}
    - {x: 0.090625, y: -0.15625, z: 0}
    - {x: -0.090625, y: 0.068749994, z: 0}
    - {x: 0.090625, y: 0.068749994, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.69531643, y: 0.007816406}
    - {x: 0.69531643, y: 0.2343711}
    - {x: 0.97655857, y: 0.007816406}
    - {x: 0.97655857, y: 0.2343711}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 144
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/3
    boundsData:
    - {x: -0.0015625022, y: -0.031250004, z: 0}
    - {x: 0.171875, y: 0.25, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.087500006, y: -0.15625, z: 0}
    - {x: 0.084375, y: -0.15625, z: 0}
    - {x: -0.087500006, y: 0.09374999, z: 0}
    - {x: 0.084375, y: 0.09374999, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7773477, y: 0.007816406}
    - {x: 0.99218357, y: 0.007816406}
    - {x: 0.7773477, y: 0.3203086}
    - {x: 0.99218357, y: 0.3203086}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 216
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/4
    boundsData:
    - {x: -0.0031250007, y: -0.031250004, z: 0}
    - {x: 0.17500001, y: 0.25, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.090625, y: -0.15625, z: 0}
    - {x: 0.084375, y: -0.15625, z: 0}
    - {x: -0.090625, y: 0.09374999, z: 0}
    - {x: 0.084375, y: 0.09374999, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.76562893, y: 0.63281643}
    - {x: 0.98437107, y: 0.63281643}
    - {x: 0.76562893, y: 0.94530857}
    - {x: 0.98437107, y: 0.94530857}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 288
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/5
    boundsData:
    - {x: -0.010937501, y: -0.018750004, z: 0}
    - {x: 0.18437502, y: 0.26875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.153125, z: 0}
    - {x: 0.081250004, y: -0.153125, z: 0}
    - {x: -0.103125006, y: 0.115624994, z: 0}
    - {x: 0.081250004, y: 0.115624994, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.65625393, y: 0.7617227}
    - {x: 0.65625393, y: 0.99218357}
    - {x: 0.99218357, y: 0.7617227}
    - {x: 0.99218357, y: 0.99218357}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 360
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/6
    boundsData:
    - {x: -0.0062500015, y: -0.0140625015, z: 0}
    - {x: 0.19375001, y: 0.278125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.153125, z: 0}
    - {x: 0.090625, y: -0.153125, z: 0}
    - {x: -0.103125006, y: 0.125, z: 0}
    - {x: 0.090625, y: 0.125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.27734765, y: 0.3828164}
    - {x: 0.27734765, y: 0.62499607}
    - {x: 0.62499607, y: 0.3828164}
    - {x: 0.62499607, y: 0.62499607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 432
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/7
    boundsData:
    - {x: -0.0046874955, y: -0.012500003, z: 0}
    - {x: 0.20312501, y: 0.28125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.153125, z: 0}
    - {x: 0.09687501, y: -0.153125, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.09687501, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.5351602, y: 0.39453515}
    - {x: 0.78905857, y: 0.39453515}
    - {x: 0.5351602, y: 0.7460898}
    - {x: 0.78905857, y: 0.7460898}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 504
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/8
    boundsData:
    - {x: -0.0046874955, y: -0.0109375045, z: 0}
    - {x: 0.20312501, y: 0.278125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.15, z: 0}
    - {x: 0.09687501, y: -0.15, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.09687501, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.007816406}
    - {x: 0.26171485, y: 0.007816406}
    - {x: 0.007816406, y: 0.35546485}
    - {x: 0.26171485, y: 0.35546485}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 576
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/9
    boundsData:
    - {x: -0.0046874955, y: -0.0109375045, z: 0}
    - {x: 0.20312501, y: 0.278125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.15, z: 0}
    - {x: 0.09687501, y: -0.15, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.09687501, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.37109765}
    - {x: 0.26171485, y: 0.37109765}
    - {x: 0.007816406, y: 0.71874607}
    - {x: 0.26171485, y: 0.71874607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 648
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/10
    boundsData:
    - {x: -0.0031250007, y: -0.0093749985, z: 0}
    - {x: 0.20625001, y: 0.27499998, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.146875, z: 0}
    - {x: 0.1, y: -0.146875, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.1, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.65625393}
    - {x: 0.007816406, y: 0.91405857}
    - {x: 0.3515586, y: 0.65625393}
    - {x: 0.3515586, y: 0.91405857}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 720
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/11
    boundsData:
    - {x: -0.0031250007, y: -0.0093749985, z: 0}
    - {x: 0.20625001, y: 0.27499998, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.146875, z: 0}
    - {x: 0.1, y: -0.146875, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.1, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.67969143, y: 0.2734414}
    - {x: 0.93749607, y: 0.2734414}
    - {x: 0.67969143, y: 0.61718357}
    - {x: 0.93749607, y: 0.61718357}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 792
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/12
    boundsData:
    - {x: -0.0031250007, y: -0.020312503, z: 0}
    - {x: 0.20625001, y: 0.296875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.16875, z: 0}
    - {x: 0.1, y: -0.16875, z: 0}
    - {x: -0.10625, y: 0.128125, z: 0}
    - {x: 0.1, y: 0.128125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.007816406}
    - {x: 0.2656211, y: 0.007816406}
    - {x: 0.007816406, y: 0.37890235}
    - {x: 0.2656211, y: 0.37890235}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 864
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/13
    boundsData:
    - {x: -0.0031250045, y: -0.018750004, z: 0}
    - {x: 0.2125, y: 0.3, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10937501, y: -0.16875, z: 0}
    - {x: 0.103125, y: -0.16875, z: 0}
    - {x: -0.10937501, y: 0.13125, z: 0}
    - {x: 0.103125, y: 0.13125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.6757852}
    - {x: 0.007816406, y: 0.9414023}
    - {x: 0.3828086, y: 0.6757852}
    - {x: 0.3828086, y: 0.9414023}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 936
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/14
    boundsData:
    - {x: -0.0031250045, y: -0.017187506, z: 0}
    - {x: 0.2125, y: 0.303125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10937501, y: -0.16875, z: 0}
    - {x: 0.103125, y: -0.16875, z: 0}
    - {x: -0.10937501, y: 0.13437499, z: 0}
    - {x: 0.103125, y: 0.13437499, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.007816406}
    - {x: 0.2734336, y: 0.007816406}
    - {x: 0.007816406, y: 0.38671485}
    - {x: 0.2734336, y: 0.38671485}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1008
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/15
    boundsData:
    - {x: -0.0062499978, y: -0.015625007, z: 0}
    - {x: 0.20625001, y: 0.30624998, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10937501, y: -0.16875, z: 0}
    - {x: 0.09687501, y: -0.16875, z: 0}
    - {x: -0.10937501, y: 0.13749999, z: 0}
    - {x: 0.09687501, y: 0.13749999, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.6835977, y: 0.27734765}
    - {x: 0.9414023, y: 0.27734765}
    - {x: 0.6835977, y: 0.6601523}
    - {x: 0.9414023, y: 0.6601523}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1080
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/16
    boundsData:
    - {x: -0.003124997, y: -0.015625007, z: 0}
    - {x: 0.20000002, y: 0.30624998, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.16875, z: 0}
    - {x: 0.09687501, y: -0.16875, z: 0}
    - {x: -0.103125006, y: 0.13749999, z: 0}
    - {x: 0.09687501, y: 0.13749999, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.4140664, y: 0.2734414}
    - {x: 0.66405857, y: 0.2734414}
    - {x: 0.4140664, y: 0.65624607}
    - {x: 0.66405857, y: 0.65624607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1152
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/17
    boundsData:
    - {x: -0.0046874955, y: -0.0124999955, z: 0}
    - {x: 0.20312501, y: 0.3125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.16875, z: 0}
    - {x: 0.09687501, y: -0.16875, z: 0}
    - {x: -0.10625, y: 0.14375001, z: 0}
    - {x: 0.09687501, y: 0.14375001, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.2890664, y: 0.007816406}
    - {x: 0.2890664, y: 0.26171485}
    - {x: 0.67968357, y: 0.007816406}
    - {x: 0.67968357, y: 0.26171485}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1224
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/18
    boundsData:
    - {x: -0.0046874955, y: -0.0093749985, z: 0}
    - {x: 0.20312501, y: 0.3125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.165625, z: 0}
    - {x: 0.09687501, y: -0.165625, z: 0}
    - {x: -0.10625, y: 0.14687501, z: 0}
    - {x: 0.09687501, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.4140664, y: 0.27734765}
    - {x: 0.6679648, y: 0.27734765}
    - {x: 0.4140664, y: 0.6679648}
    - {x: 0.6679648, y: 0.6679648}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1296
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/19
    boundsData:
    - {x: -0.0031250007, y: -0.0093749985, z: 0}
    - {x: 0.20625001, y: 0.3125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.165625, z: 0}
    - {x: 0.1, y: -0.165625, z: 0}
    - {x: -0.10625, y: 0.14687501, z: 0}
    - {x: 0.1, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.40234765}
    - {x: 0.007816406, y: 0.6601523}
    - {x: 0.3984336, y: 0.40234765}
    - {x: 0.3984336, y: 0.6601523}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1368
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/20
    boundsData:
    - {x: -0.0046874955, y: -0.0093749985, z: 0}
    - {x: 0.20312501, y: 0.3125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.165625, z: 0}
    - {x: 0.09687501, y: -0.165625, z: 0}
    - {x: -0.10625, y: 0.14687501, z: 0}
    - {x: 0.09687501, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.3984414, y: 0.6835977}
    - {x: 0.3984414, y: 0.93749607}
    - {x: 0.78905857, y: 0.6835977}
    - {x: 0.78905857, y: 0.93749607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1440
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/21
    boundsData:
    - {x: -0.0046874993, y: -0.006249994, z: 0}
    - {x: 0.196875, y: 0.3125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.1625, z: 0}
    - {x: 0.09375001, y: -0.1625, z: 0}
    - {x: -0.103125006, y: 0.15, z: 0}
    - {x: 0.09375001, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.39453515}
    - {x: 0.007816406, y: 0.64062107}
    - {x: 0.3984336, y: 0.39453515}
    - {x: 0.3984336, y: 0.64062107}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1512
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/22
    boundsData:
    - {x: -0.0062499978, y: -0.0046874955, z: 0}
    - {x: 0.20000002, y: 0.309375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.10625, y: -0.159375, z: 0}
    - {x: 0.09375001, y: -0.159375, z: 0}
    - {x: -0.10625, y: 0.15, z: 0}
    - {x: 0.09375001, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.2812539, y: 0.007816406}
    - {x: 0.2812539, y: 0.2578086}
    - {x: 0.6679648, y: 0.007816406}
    - {x: 0.6679648, y: 0.2578086}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1584
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/23
    boundsData:
    - {x: -0.0046874993, y: -0.006249994, z: 0}
    - {x: 0.196875, y: 0.30625, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.159375, z: 0}
    - {x: 0.09375001, y: -0.159375, z: 0}
    - {x: -0.103125006, y: 0.14687501, z: 0}
    - {x: 0.09375001, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.007816406}
    - {x: 0.25390235, y: 0.007816406}
    - {x: 0.007816406, y: 0.3906211}
    - {x: 0.25390235, y: 0.3906211}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1656
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/24
    boundsData:
    - {x: -0.003124997, y: -0.003124997, z: 0}
    - {x: 0.20000002, y: 0.30625, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.15625, z: 0}
    - {x: 0.09687501, y: -0.15625, z: 0}
    - {x: -0.103125006, y: 0.15, z: 0}
    - {x: 0.09687501, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.3671914, y: 0.67187893}
    - {x: 0.3671914, y: 0.92187107}
    - {x: 0.74999607, y: 0.67187893}
    - {x: 0.74999607, y: 0.92187107}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1728
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/25
    boundsData:
    - {x: -0.003124997, y: -0.003124997, z: 0}
    - {x: 0.20000002, y: 0.3, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125006, y: -0.153125, z: 0}
    - {x: 0.09687501, y: -0.153125, z: 0}
    - {x: -0.103125006, y: 0.14687501, z: 0}
    - {x: 0.09687501, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.4062539}
    - {x: 0.2578086, y: 0.4062539}
    - {x: 0.007816406, y: 0.78124607}
    - {x: 0.2578086, y: 0.78124607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1800
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/26
    boundsData:
    - {x: -0.0015624948, y: -0.0015624985, z: 0}
    - {x: 0.196875, y: 0.296875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.1, y: -0.15, z: 0}
    - {x: 0.09687501, y: -0.15, z: 0}
    - {x: -0.1, y: 0.14687501, z: 0}
    - {x: 0.09687501, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.26953515, y: 0.007816406}
    - {x: 0.51562107, y: 0.007816406}
    - {x: 0.26953515, y: 0.37890235}
    - {x: 0.51562107, y: 0.37890235}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1872
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/27
    boundsData:
    - {x: -0.0015624948, y: 0.001562506, z: 0}
    - {x: 0.196875, y: 0.296875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.1, y: -0.146875, z: 0}
    - {x: 0.09687501, y: -0.146875, z: 0}
    - {x: -0.1, y: 0.15, z: 0}
    - {x: 0.09687501, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.2734414, y: 0.39453515}
    - {x: 0.5195273, y: 0.39453515}
    - {x: 0.2734414, y: 0.76562107}
    - {x: 0.5195273, y: 0.76562107}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 1944
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/28
    boundsData:
    - {x: -0.003124997, y: 0.001562506, z: 0}
    - {x: 0.19375001, y: 0.296875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.1, y: -0.146875, z: 0}
    - {x: 0.09375001, y: -0.146875, z: 0}
    - {x: -0.1, y: 0.15, z: 0}
    - {x: 0.09375001, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.53125393, y: 0.007816406}
    - {x: 0.77343357, y: 0.007816406}
    - {x: 0.53125393, y: 0.37890235}
    - {x: 0.77343357, y: 0.37890235}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2016
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/29
    boundsData:
    - {x: -0.0046874993, y: 0.001562506, z: 0}
    - {x: 0.19062501, y: 0.284375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.1, y: -0.140625, z: 0}
    - {x: 0.090625, y: -0.140625, z: 0}
    - {x: -0.1, y: 0.14375001, z: 0}
    - {x: 0.090625, y: 0.14375001, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.73437893}
    - {x: 0.007816406, y: 0.9726523}
    - {x: 0.36327735, y: 0.73437893}
    - {x: 0.36327735, y: 0.9726523}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2088
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/30
    boundsData:
    - {x: -0.0031250007, y: 0.0031250045, z: 0}
    - {x: 0.1875, y: 0.28750002, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.096875004, y: -0.140625, z: 0}
    - {x: 0.090625, y: -0.140625, z: 0}
    - {x: -0.096875004, y: 0.14687501, z: 0}
    - {x: 0.090625, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.27734765, y: 0.007816406}
    - {x: 0.5117148, y: 0.007816406}
    - {x: 0.27734765, y: 0.3671836}
    - {x: 0.5117148, y: 0.3671836}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2160
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/31
    boundsData:
    - {x: -0.0031250007, y: 0.004687503, z: 0}
    - {x: 0.1875, y: 0.284375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.096875004, y: -0.1375, z: 0}
    - {x: 0.090625, y: -0.1375, z: 0}
    - {x: -0.096875004, y: 0.14687501, z: 0}
    - {x: 0.090625, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.5273477, y: 0.007816406}
    - {x: 0.7617148, y: 0.007816406}
    - {x: 0.5273477, y: 0.36327735}
    - {x: 0.7617148, y: 0.36327735}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2232
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/32
    boundsData:
    - {x: -0.0031250007, y: 0.043750007, z: 0}
    - {x: 0.1875, y: 0.20000002, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.096875004, y: -0.05625, z: 0}
    - {x: 0.090625, y: -0.05625, z: 0}
    - {x: -0.096875004, y: 0.14375001, z: 0}
    - {x: 0.090625, y: 0.14375001, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.6835977, y: 0.007816406}
    - {x: 0.6835977, y: 0.2421836}
    - {x: 0.9335898, y: 0.007816406}
    - {x: 0.9335898, y: 0.2421836}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
    materialId: 1
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2304
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/33
    boundsData:
    - {x: -0.0031250007, y: 0.06250001, z: 0}
    - {x: 0.1875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.096875004, y: -0.021875, z: 0}
    - {x: 0.090625, y: -0.021875, z: 0}
    - {x: -0.096875004, y: 0.14687501, z: 0}
    - {x: 0.090625, y: 0.14687501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.4062539, y: 0.78125393}
    - {x: 0.64062107, y: 0.78125393}
    - {x: 0.4062539, y: 0.99218357}
    - {x: 0.64062107, y: 0.99218357}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2376
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/34
    boundsData:
    - {x: -0.0031250007, y: 0.065625004, z: 0}
    - {x: 0.1875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.096875004, y: -0.018750003, z: 0}
    - {x: 0.090625, y: -0.018750003, z: 0}
    - {x: -0.096875004, y: 0.15, z: 0}
    - {x: 0.090625, y: 0.15, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7773477, y: 0.3359414}
    - {x: 0.7773477, y: 0.57030857}
    - {x: 0.9882773, y: 0.3359414}
    - {x: 0.9882773, y: 0.57030857}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2448
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/35
    boundsData:
    - {x: 0.0015624985, y: 0.06875, z: 0}
    - {x: 0.165625, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.081250004, y: -0.015624995, z: 0}
    - {x: 0.084375, y: -0.015624995, z: 0}
    - {x: -0.081250004, y: 0.153125, z: 0}
    - {x: 0.084375, y: 0.153125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.64062893, y: 0.58594143}
    - {x: 0.64062893, y: 0.7929648}
    - {x: 0.85155857, y: 0.58594143}
    - {x: 0.85155857, y: 0.7929648}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
    materialId: 3
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2520
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/36
    boundsData:
    - {x: 0.0078125, y: 0.071875, z: 0}
    - {x: 0.153125, y: 0.16250001, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.06875, y: -0.009375001, z: 0}
    - {x: 0.084375, y: -0.009375001, z: 0}
    - {x: -0.06875, y: 0.153125, z: 0}
    - {x: 0.084375, y: 0.153125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.007816406, y: 0.79687893}
    - {x: 0.007816406, y: 0.9882773}
    - {x: 0.2109336, y: 0.79687893}
    - {x: 0.2109336, y: 0.9882773}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2592
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/37
    boundsData:
    - {x: 0.0062499978, y: 0.056250002, z: 0}
    - {x: 0.15625, y: 0.13125001, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.071875006, y: -0.009375001, z: 0}
    - {x: 0.084375, y: -0.009375001, z: 0}
    - {x: -0.071875006, y: 0.121875, z: 0}
    - {x: 0.084375, y: 0.121875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.2265664, y: 0.79687893}
    - {x: 0.2265664, y: 0.99218357}
    - {x: 0.3906211, y: 0.79687893}
    - {x: 0.3906211, y: 0.99218357}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2664
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/38
    boundsData:
    - {x: 0.0078125, y: 0.054687496, z: 0}
    - {x: 0.15937501, y: 0.121874996, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.071875006, y: -0.0062500043, z: 0}
    - {x: 0.087500006, y: -0.0062500043, z: 0}
    - {x: -0.071875006, y: 0.115624994, z: 0}
    - {x: 0.087500006, y: 0.115624994, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.80469143, y: 0.6757852}
    - {x: 0.80469143, y: 0.87499607}
    - {x: 0.9570273, y: 0.6757852}
    - {x: 0.9570273, y: 0.87499607}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2736
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/39
    boundsData:
    - {x: 0.026562499, y: 0.057812504, z: 0}
    - {x: 0.11562501, y: 0.121875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.031250004, y: -0.003124997, z: 0}
    - {x: 0.084375, y: -0.003124997, z: 0}
    - {x: -0.031250004, y: 0.118750006, z: 0}
    - {x: 0.084375, y: 0.118750006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.80469143, y: 0.3593789}
    - {x: 0.80469143, y: 0.5039023}
    - {x: 0.9570273, y: 0.3593789}
    - {x: 0.9570273, y: 0.5039023}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2808
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/40
    boundsData:
    - {x: 0.029687501, y: 0.057812504, z: 0}
    - {x: 0.11562501, y: 0.121875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.028125001, y: -0.003124997, z: 0}
    - {x: 0.087500006, y: -0.003124997, z: 0}
    - {x: -0.028125001, y: 0.118750006, z: 0}
    - {x: 0.087500006, y: 0.118750006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.80469143, y: 0.5195352}
    - {x: 0.80469143, y: 0.66405857}
    - {x: 0.9570273, y: 0.5195352}
    - {x: 0.9570273, y: 0.66405857}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
    materialId: 2
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2880
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/41
    boundsData:
    - {x: -0.0031250017, y: 0.1171875, z: 0}
    - {x: 0.0062500034, y: 0.003125012, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.0062500034, y: 0.115624994, z: 0}
    - {x: 0, y: 0.115624994, z: 0}
    - {x: -0.0062500034, y: 0.118750006, z: 0}
    - {x: 0, y: 0.118750006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.2890664, y: 0.27734765}
    - {x: 0.2968711, y: 0.27734765}
    - {x: 0.2890664, y: 0.2812461}
    - {x: 0.2968711, y: 0.2812461}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 2952
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/42
    boundsData:
    - {x: 0.0015625008, y: 0.0015624985, z: 0}
    - {x: 0.0031250017, y: 0.003124997, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: 0, y: 0, z: 0}
    - {x: 0.0031250017, y: 0, z: 0}
    - {x: 0, y: 0.003124997, z: 0}
    - {x: 0.0031250017, y: 0.003124997, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.3125039, y: 0.27734765}
    - {x: 0.30077735, y: 0.27734765}
    - {x: 0.3125039, y: 0.2656211}
    - {x: 0.30077735, y: 0.2656211}
    normalizedUvs:
    - {x: 1, y: 1}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 0, y: 0}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 3024
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_44FR/43
    boundsData:
    - {x: 0.0015625008, y: 0.0015624985, z: 0}
    - {x: 0.0031250017, y: 0.003124997, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.22500001, y: 0.34375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: 0, y: 0, z: 0}
    - {x: 0.0031250017, y: 0, z: 0}
    - {x: 0, y: 0.003124997, z: 0}
    - {x: 0.0031250017, y: 0.003124997, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.3125039, y: 0.27734765}
    - {x: 0.30077735, y: 0.27734765}
    - {x: 0.3125039, y: 0.2656211}
    - {x: 0.30077735, y: 0.2656211}
    normalizedUvs:
    - {x: 1, y: 1}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 0, y: 0}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
    materialId: 0
    sourceTextureGUID: f38c1e9651d69654c87ea0956466e36d
    extractRegion: 1
    regionX: 3096
    regionY: 0
    regionW: 72
    regionH: 110
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  premultipliedAlpha: 1
  material: {fileID: 0}
  materials:
  - {fileID: 2100000, guid: be7e535ced7e5e6429a8d04c2496f423, type: 2}
  - {fileID: 2100000, guid: c57dd7dda028a3a42967f275c725424f, type: 2}
  - {fileID: 2100000, guid: 896eb8962d2295b4d939ad2d9e277a3f, type: 2}
  - {fileID: 2100000, guid: 5a9c40ea0fae93a4cb80d38f4c2a684e, type: 2}
  textures:
  - {fileID: 2800000, guid: cd6fbfbe8894ff04f9643675be26a573, type: 3}
  - {fileID: 2800000, guid: 781c2c74baf34e643bf7fdc51b2276ea, type: 3}
  - {fileID: 2800000, guid: a57fd406b36fc6d44b38c2a82b620ccc, type: 3}
  - {fileID: 2800000, guid: 83ed0f7a30dec5040bc629f357183e4a, type: 3}
  pngTextures: []
  materialPngTextureId: 00000000010000000200000003000000
  textureFilterMode: 1
  textureMipMaps: 0
  allowMultipleAtlases: 1
  spriteCollectionGUID: 6abdf3241ce81204293a7d50f971e3d7
  spriteCollectionName: DemoMultiAtlas
  assetName: 
  loadable: 0
  invOrthoSize: 1
  halfTargetHeight: 320
  buildKey: 698933273
  dataGuid: e40f26e01721f87448061d44beed20dd
  managedSpriteCollection: 0
  hasPlatformData: 0
  spriteCollectionPlatforms: []
  spriteCollectionPlatformGUIDs: []
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

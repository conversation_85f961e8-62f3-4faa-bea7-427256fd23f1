%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: MultiAtlasDemoAnimation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ecaffc43a5d2b1d47858e3fb687e7271, type: 1}
  m_Name: 
  clips:
  - name: explosion
    frames:
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 0
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 1
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 2
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 3
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 4
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 5
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 6
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 7
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 8
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 9
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 10
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 11
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 12
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 13
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 14
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 15
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 16
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 17
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 18
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 19
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 20
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 21
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 22
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 23
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 24
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 25
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 26
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 27
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 28
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 29
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 30
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 31
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 32
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 33
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 34
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 35
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 36
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 37
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 38
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 39
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 40
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 41
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 42
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    - spriteCollection: {fileID: 11400000, guid: e40f26e01721f87448061d44beed20dd,
        type: 2}
      spriteId: 43
      triggerEvent: 0
      eventInfo: 
      eventInt: 0
      eventFloat: 0
    fps: 30
    loopStart: 0
    wrapMode: 0
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
  m_IsExploded: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

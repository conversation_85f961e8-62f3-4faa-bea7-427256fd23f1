%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: DemoSpiderSpriteCollection
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84c7a4b4ee15890498f818262bca0312, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  textures: []
  textureRefs: []
  spriteSheets: []
  fonts: []
  defaults:
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    anchor: 4
    pad: 0
    colliderType: 0
  platforms:
  - name: 
    spriteCollection: {fileID: 0}
  managedSpriteCollection: 0
  linkParent: {fileID: 0}
  loadable: 0
  atlasFormat: 0
  maxTextureSize: 1024
  forceTextureSize: 0
  forcedTextureWidth: 1024
  forcedTextureHeight: 1024
  textureCompression: 0
  atlasWidth: 256
  atlasHeight: 512
  forceSquareAtlas: 0
  atlasWastage: 66.05301
  allowMultipleAtlases: 0
  removeDuplicates: 1
  textureParams:
  - name: body
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 17a4fe5393d6f6048b0a96839cd54591, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 72
    anchorY: 127
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: lowerleg
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 731aac236e6d1724fb48cd35227e8998, type: 3}
    materialId: 0
    anchor: 1
    anchorX: 6
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: mouth
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 871eb2b697bff4f4e832b95668b9b3cc, type: 3}
    materialId: 0
    anchor: 6
    anchorX: 0
    anchorY: 58
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: upperleg
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 75f62f68bfc69b04d876f87672036e09, type: 3}
    materialId: 0
    anchor: 1
    anchorX: 10
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  spriteCollection: {fileID: 11400000, guid: 822f1f9eedf119f4eaca33509ba7b549, type: 2}
  premultipliedAlpha: 1
  altMaterials:
  - {fileID: 2100000, guid: 8a2dd588da2bb8e44a189a90333944d7, type: 2}
  atlasMaterials:
  - {fileID: 2100000, guid: 8a2dd588da2bb8e44a189a90333944d7, type: 2}
  atlasTextures:
  - {fileID: 2800000, guid: 657c06b4a37f4bb45a24cc96727be5b8, type: 3}
  atlasTextureFiles: []
  useTk2dCamera: 0
  targetHeight: 640
  targetOrthoSize: 1
  sizeDef:
    type: 0
    orthoSize: 1
    pixelsPerMeter: 100
    width: 960
    height: 640
  globalScale: 1
  globalTextureRescale: 1
  attachPointTestSprites: []
  pixelPerfectPointSampled: 0
  filterMode: 1
  wrapMode: 1
  userDefinedTextureSettings: 1
  mipmapEnabled: 0
  anisoLevel: 1
  physicsEngine: 0
  physicsDepth: 0.1
  disableTrimming: 0
  disableRotation: 0
  normalGenerationMode: 0
  padAmount: -1
  autoUpdate: 1
  editorDisplayScale: 1
  version: 4
  assetName: 
  linkedSpriteCollections: []
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: spideranimtest
  serializedVersion: 3
  m_Compressed: 0
  m_RotationCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: 0, w: 1}
        inSlope: {x: 0, y: 0, z: -.57022959, w: -.00271081901}
        outSlope: {x: 0, y: 0, z: -.57022959, w: -.00271081901}
        tangentMode: 1044643840
      - time: .316666722
        value: {x: 0, y: 0, z: -.179595709, w: .983740509}
        inSlope: {x: 0, y: 0, z: -.326055765, w: -.059382271}
        outSlope: {x: 0, y: 0, z: -.326055765, w: -.059382271}
        tangentMode: 0
      - time: .633333385
        value: {x: 0, y: 0, z: -.159731463, w: .987160504}
        inSlope: {x: 0, y: 0, z: .614257693, w: .0990290344}
        outSlope: {x: 0, y: 0, z: .614257693, w: .0990290344}
        tangentMode: 0
      - time: .833333313
        value: {x: 0, y: 0, z: 0, w: 1}
        inSlope: {x: 0, y: 0, z: .765763938, w: .00488516642}
        outSlope: {x: 0, y: 0, z: .765763938, w: .00488516642}
        tangentMode: 1631760
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthr
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: 0, w: 1}
        inSlope: {x: 0, y: 0, z: .548963487, w: -.00251054741}
        outSlope: {x: 0, y: 0, z: .548963487, w: -.00251054741}
        tangentMode: 0
      - time: .350000083
        value: {x: 0, y: 0, z: .182109997, w: .983278155}
        inSlope: {x: 0, y: 0, z: .253092587, w: -.0468492135}
        outSlope: {x: 0, y: 0, z: .253092587, w: -.0468492135}
        tangentMode: 0
      - time: .683333337
        value: {x: 0, y: 0, z: .169288695, w: .985566497}
        inSlope: {x: 0, y: 0, z: -.575081825, w: .0981385484}
        outSlope: {x: 0, y: 0, z: -.575081825, w: .0981385484}
        tangentMode: 0
      - time: .866666675
        value: {x: 0, y: 0, z: 0, w: 1}
        inSlope: {x: 0, y: 0, z: -.92782408, w: .00717394473}
        outSlope: {x: 0, y: 0, z: -.92782408, w: .00717394473}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthl
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: .872562706, w: .488502085}
        inSlope: {x: 0, y: 0, z: .00640511466, w: -.0114405146}
        outSlope: {x: 0, y: 0, z: .00640511466, w: -.0114405146}
        tangentMode: 166869872
      - time: .833333194
        value: {x: 0, y: 0, z: .877847672, w: .478939891}
        inSlope: {x: 0, y: 0, z: .00627995143, w: -.011508476}
        outSlope: {x: 0, y: 0, z: .00627995143, w: -.011508476}
        tangentMode: 200100560
      - time: .866666675
        value: {x: 0, y: 0, z: .878056943, w: .478556216}
        inSlope: {x: 0, y: 0, z: .00627630763, w: -.0115119284}
        outSlope: {x: 0, y: 0, z: .00627630763, w: -.0115119284}
        tangentMode: 199169712
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4r
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: .245629057, w: .969363928}
        inSlope: {x: 0, y: 0, z: -.0585660301, w: .0148057928}
        outSlope: {x: 0, y: 0, z: -.0585660301, w: .0148057928}
        tangentMode: 166869872
      - time: .833333194
        value: {x: 0, y: 0, z: .196539253, w: .98049593}
        inSlope: {x: 0, y: 0, z: -.0592312776, w: .0118732564}
        outSlope: {x: 0, y: 0, z: -.0592312776, w: .0118732564}
        tangentMode: 200100560
      - time: .866666675
        value: {x: 0, y: 0, z: .194564477, w: .980889738}
        inSlope: {x: 0, y: 0, z: -.0592494197, w: .0117837228}
        outSlope: {x: 0, y: 0, z: -.0592494197, w: .0117837228}
        tangentMode: 199169712
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4r/leg4rb
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: -.888152242, w: .459549367}
        inSlope: {x: 0, y: 0, z: -.0648593903, w: -.125716925}
        outSlope: {x: 0, y: 0, z: -.0648593903, w: -.125716925}
        tangentMode: 166869872
      - time: .833333194
        value: {x: 0, y: 0, z: -.936037064, w: .351901442}
        inSlope: {x: 0, y: 0, z: -.0497818477, w: -.132418096}
        outSlope: {x: 0, y: 0, z: -.0497818477, w: -.132418096}
        tangentMode: 200100560
      - time: .866666675
        value: {x: 0, y: 0, z: -.937686026, w: .347483695}
        inSlope: {x: 0, y: 0, z: -.04930925, w: -.132582083}
        outSlope: {x: 0, y: 0, z: -.04930925, w: -.132582083}
        tangentMode: 199169712
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 0, y: 0, z: .20715642, w: -.978307843}
        inSlope: {x: 0, y: 0, z: -.501166284, w: -.103890888}
        outSlope: {x: 0, y: 0, z: -.501166284, w: -.103890888}
        tangentMode: 165773040
      - time: .350000083
        value: {x: 0, y: 0, z: .0295256693, w: -.999564052}
        inSlope: {x: 0, y: 0, z: -.511592925, w: -.0151115526}
        outSlope: {x: 0, y: 0, z: -.511592925, w: -.0151115526}
        tangentMode: 165592272
      - time: .383333325
        value: {x: 0, y: 0, z: .0209980384, w: -.999779522}
        inSlope: {x: 0, y: 0, z: 0, w: 0}
        outSlope: {x: 0, y: 0, z: 0, w: 0}
        tangentMode: 165186840
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l/leg4lb
  m_CompressedRotationCurves: []
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: -.0136563554, y: .350515902, z: .0163834821}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 1032324882
      - time: .583333313
        value: {x: -.0136563554, y: .350515902, z: .0163834821}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 13
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthr
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: .00431232899, y: .350515902, z: .0163834821}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: -1
      - time: .666666687
        value: {x: .00431232899, y: .350515902, z: .0163834821}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: -1
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthl
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: .0487378985, y: .332443446, z: .0293227285}
        inSlope: {x: 5.90649405e-39, y: 0, z: 0}
        outSlope: {x: 5.90649405e-39, y: 0, z: 0}
        tangentMode: 1080033280
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4r
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: -.0402190462, y: .32076022, z: .0293227285}
        inSlope: {x: 5.90649405e-39, y: 0, z: 0}
        outSlope: {x: 5.90649405e-39, y: 0, z: 0}
        tangentMode: 917516
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: .00212744903, y: -.284131527, z: 0}
        inSlope: {x: 5.90649405e-39, y: 0, z: 0}
        outSlope: {x: 5.90649405e-39, y: 0, z: 0}
        tangentMode: 1080033280
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l/leg4lb
  m_ScaleCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 486
      - time: .583333313
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 247690448
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthr
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 1350
      - time: .666666687
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 0}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: mouthl
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 5.90649405e-39}
        outSlope: {x: 0, y: 0, z: 5.90649405e-39}
        tangentMode: 1080033280
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4r
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 5.90649405e-39}
        outSlope: {x: 0, y: 0, z: 5.90649405e-39}
        tangentMode: 917516
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: {x: 1, y: 1, z: 1}
        inSlope: {x: 0, y: 0, z: 5.90649405e-39}
        outSlope: {x: 0, y: 0, z: 5.90649405e-39}
        tangentMode: 917516
      m_PreInfinity: 2
      m_PostInfinity: 2
    path: leg4l/leg4lb
  m_FloatCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -.0136563554
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: -.0136563554
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.x
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .350515902
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: .350515902
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.y
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .0163834821
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: .0163834821
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .316666722
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .633333385
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .316666722
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .633333385
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: -.57022959
        outSlope: -.57022959
        tangentMode: 0
      - time: .316666722
        value: -.179595709
        inSlope: -.326055765
        outSlope: -.326055765
        tangentMode: 0
      - time: .633333385
        value: -.159731463
        inSlope: .614257693
        outSlope: .614257693
        tangentMode: 0
      - time: .833333313
        value: 0
        inSlope: .765763938
        outSlope: .765763938
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.00271081901
        outSlope: -.00271081901
        tangentMode: 0
      - time: .316666722
        value: .983740509
        inSlope: -.059382271
        outSlope: -.059382271
        tangentMode: 0
      - time: .633333385
        value: .987160504
        inSlope: .0990290344
        outSlope: .0990290344
        tangentMode: 0
      - time: .833333313
        value: 1
        inSlope: .00488516642
        outSlope: .00488516642
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .00431232899
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: .00431232899
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.x
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .350515902
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: .350515902
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.y
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .0163834821
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: .0163834821
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .350000083
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .683333337
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .350000083
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .683333337
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: .548963487
        outSlope: .548963487
        tangentMode: 0
      - time: .350000083
        value: .182109997
        inSlope: .253092587
        outSlope: .253092587
        tangentMode: 0
      - time: .683333337
        value: .169288695
        inSlope: -.575081825
        outSlope: -.575081825
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: -.92782408
        outSlope: -.92782408
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: -.00251054741
        outSlope: -.00251054741
        tangentMode: 0
      - time: .350000083
        value: .983278155
        inSlope: -.0468492135
        outSlope: -.0468492135
        tangentMode: 0
      - time: .683333337
        value: .985566497
        inSlope: .0981385484
        outSlope: .0981385484
        tangentMode: 0
      - time: .866666675
        value: 1
        inSlope: .00717394473
        outSlope: .00717394473
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .0487378985
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.x
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .332443446
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.y
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .0293227285
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .872562706
        inSlope: .00640511466
        outSlope: .00640511466
        tangentMode: 0
      - time: .833333194
        value: .877847672
        inSlope: .00627995143
        outSlope: .00627995143
        tangentMode: 0
      - time: .866666675
        value: .878056943
        inSlope: .00627630763
        outSlope: .00627630763
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .488502085
        inSlope: -.0114405146
        outSlope: -.0114405146
        tangentMode: 0
      - time: .833333194
        value: .478939891
        inSlope: -.011508476
        outSlope: -.011508476
        tangentMode: 0
      - time: .866666675
        value: .478556216
        inSlope: -.0115119284
        outSlope: -.0115119284
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .245629057
        inSlope: -.0585660301
        outSlope: -.0585660301
        tangentMode: 0
      - time: .833333194
        value: .196539253
        inSlope: -.0592312776
        outSlope: -.0592312776
        tangentMode: 0
      - time: .866666675
        value: .194564477
        inSlope: -.0592494197
        outSlope: -.0592494197
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .969363928
        inSlope: .0148057928
        outSlope: .0148057928
        tangentMode: 0
      - time: .833333194
        value: .98049593
        inSlope: .0118732564
        outSlope: .0118732564
        tangentMode: 0
      - time: .866666675
        value: .980889738
        inSlope: .0117837228
        outSlope: .0117837228
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -.0402190462
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.x
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .32076022
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.y
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .0293227285
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .833333194
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -.888152242
        inSlope: -.0648593903
        outSlope: -.0648593903
        tangentMode: 0
      - time: .833333194
        value: -.936037064
        inSlope: -.0497818477
        outSlope: -.0497818477
        tangentMode: 0
      - time: .866666675
        value: -.937686026
        inSlope: -.04930925
        outSlope: -.04930925
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .459549367
        inSlope: -.125716925
        outSlope: -.125716925
        tangentMode: 0
      - time: .833333194
        value: .351901442
        inSlope: -.132418096
        outSlope: -.132418096
        tangentMode: 0
      - time: .866666675
        value: .347483695
        inSlope: -.132582083
        outSlope: -.132582083
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .00212744903
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.x
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -.284131527
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.y
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalPosition.z
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.z
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.y
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 1
        inSlope: 5.90649405e-39
        outSlope: 5.90649405e-39
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalScale.x
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .350000083
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .383333325
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.x
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .350000083
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - time: .383333325
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.y
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: .20715642
        inSlope: -.501166284
        outSlope: -.501166284
        tangentMode: 0
      - time: .350000083
        value: .0295256693
        inSlope: -.511592925
        outSlope: -.511592925
        tangentMode: 0
      - time: .383333325
        value: .0209980384
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.z
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -.978307843
        inSlope: -.103890888
        outSlope: -.103890888
        tangentMode: 0
      - time: .350000083
        value: -.999564052
        inSlope: -.0151115526
        outSlope: -.0151115526
        tangentMode: 0
      - time: .383333325
        value: -.999779522
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: m_LocalRotation.w
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  m_EulerEditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .333333343
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .833333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .333333343
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .583333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .833333313
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: -63.8285561
        outSlope: -63.8285561
        tangentMode: 10
      - time: .333333343
        value: -21.276186
        inSlope: -31.914278
        outSlope: -31.914278
        tangentMode: 10
      - time: .583333313
        value: -21.276186
        inSlope: 42.552372
        outSlope: 42.552372
        tangentMode: 10
      - time: .833333313
        value: 0
        inSlope: 85.104744
        outSlope: 85.104744
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: mouthr
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .333333343
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .333333343
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .666666687
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 61.4480667
        outSlope: 61.4480667
        tangentMode: 10
      - time: .333333343
        value: 20.4826889
        inSlope: 30.7240334
        outSlope: 30.7240334
        tangentMode: 10
      - time: .666666687
        value: 20.4826889
        inSlope: -51.2067261
        outSlope: -51.2067261
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: -102.413452
        outSlope: -102.413452
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: mouthl
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 121.515656
        inSlope: 1.50236273
        outSlope: 1.50236273
        tangentMode: 10
      - time: .866666675
        value: 122.817703
        inSlope: 1.50236273
        outSlope: 1.50236273
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: leg4r
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 28.4380245
        inSlope: -6.92245388
        outSlope: -6.92245388
        tangentMode: 10
      - time: .866666675
        value: 22.4385643
        inSlope: -6.92245388
        outSlope: -6.92245388
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: leg4r/leg4rb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .866666675
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: -125.283936
        inSlope: -16.2105026
        outSlope: -16.2105026
        tangentMode: 10
      - time: .866666675
        value: -139.333038
        inSlope: -16.2105026
        outSlope: -16.2105026
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: leg4l
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .383333325
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.x
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      - time: .383333325
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.y
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - time: 0
        value: 336.08847
        inSlope: 58.6504288
        outSlope: 58.6504288
        tangentMode: 10
      - time: .366666675
        value: 357.593628
        inSlope: 58.6504288
        outSlope: 58.6504288
        tangentMode: 10
      m_PreInfinity: 2
      m_PostInfinity: 2
    attribute: localEulerAnglesBaked.z
    path: leg4l/leg4lb
    classID: 4
    script: {fileID: 0}
  m_Events: []
--- !u!1002 &7400001
EditorExtensionImpl:
  serializedVersion: 6

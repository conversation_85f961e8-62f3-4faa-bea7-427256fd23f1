%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!1 &13
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 25}
  - 23: {fileID: 39}
  - 33: {fileID: 48}
  - 114: {fileID: 65}
  m_Layer: 8
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &14
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 26}
  - 33: {fileID: 49}
  - 23: {fileID: 40}
  - 114: {fileID: 67}
  - 114: {fileID: 66}
  - 82: {fileID: 59}
  m_Layer: 8
  m_Name: Button_Stop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &15
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 27}
  - 33: {fileID: 50}
  - 23: {fileID: 41}
  - 114: {fileID: 68}
  - 114: {fileID: 69}
  - 82: {fileID: 60}
  m_Layer: 8
  m_Name: Button_SpinLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &16
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 28}
  - 23: {fileID: 42}
  - 33: {fileID: 51}
  - 114: {fileID: 70}
  m_Layer: 8
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &17
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 29}
  - 23: {fileID: 43}
  - 33: {fileID: 52}
  - 114: {fileID: 71}
  m_Layer: 8
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &18
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 30}
  - 33: {fileID: 53}
  - 23: {fileID: 44}
  - 114: {fileID: 73}
  - 114: {fileID: 72}
  - 82: {fileID: 61}
  m_Layer: 8
  m_Name: Button_SpinRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &19
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 31}
  - 23: {fileID: 45}
  - 33: {fileID: 54}
  - 114: {fileID: 74}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &20
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 32}
  - 23: {fileID: 46}
  - 33: {fileID: 55}
  - 114: {fileID: 75}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &21
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 33}
  - 108: {fileID: 63}
  m_Layer: 0
  m_Name: Directional light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &22
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 34}
  - 33: {fileID: 56}
  - 65: {fileID: 57}
  - 23: {fileID: 47}
  - 114: {fileID: 76}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &23
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 35}
  - 20: {fileID: 37}
  m_Layer: 0
  m_Name: GuiCamera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &24
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 36}
  - 20: {fileID: 38}
  - 92: {fileID: 62}
  - 124: {fileID: 64}
  - 81: {fileID: 58}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &25
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -.0678718686, z: -.0200004578}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 26}
--- !u!4 &26
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -.850000024, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 25}
  m_Father: {fileID: 35}
--- !u!4 &27
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -.5, y: .850000024, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 28}
  m_Father: {fileID: 35}
--- !u!4 &28
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -.0678718686, z: -.0200004578}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 27}
--- !u!4 &29
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -.0678718686, z: -.0200004578}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 30}
--- !u!4 &30
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: .5, y: .850000024, z: 10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 29}
  m_Father: {fileID: 35}
--- !u!4 &31
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_LocalRotation: {x: -8.42125214e-09, y: -5.25076338e-09, z: -3.85753918e-09, w: -1}
  m_LocalPosition: {x: .0892902985, y: .567292571, z: .400244951}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_Children: []
  m_Father: {fileID: 34}
--- !u!4 &32
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_LocalRotation: {x: 0, y: .707106829, z: 0, w: -.707106829}
  m_LocalPosition: {x: .533802927, y: .148836508, z: 4.44780319e-08}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_Children: []
  m_Father: {fileID: 34}
--- !u!4 &33
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -.0886312574, y: -.00745813549, z: -.00155007839}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!4 &34
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_LocalRotation: {x: -.282526493, y: .39357242, z: .0378227681, w: .873984575}
  m_LocalPosition: {x: -.0886312574, y: 0, z: -.00155007839}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 32}
  - {fileID: 31}
  m_Father: {fileID: 0}
--- !u!4 &35
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 16.6300774, y: 0, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 30}
  - {fileID: 27}
  - {fileID: 26}
  m_Father: {fileID: 0}
--- !u!4 &36
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -4.71003246}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!20 &37
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 3
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: -0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 10
  field of view: 60
  orthographic: 1
  orthographic size: 1
  m_Depth: 1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 256
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!20 &38
Camera:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 1
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967039
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &39
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &40
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &41
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &42
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &43
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &44
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &45
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: fa89605a58d1b2e4cbef538581ac12f3, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &46
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &47
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 10302, guid: 0000000000000000e000000000000000, type: 0}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &48
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_Mesh: {fileID: 0}
--- !u!33 &49
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Mesh: {fileID: 0}
--- !u!33 &50
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Mesh: {fileID: 0}
--- !u!33 &51
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Mesh: {fileID: 0}
--- !u!33 &52
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Mesh: {fileID: 0}
--- !u!33 &53
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Mesh: {fileID: 0}
--- !u!33 &54
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Mesh: {fileID: 0}
--- !u!33 &55
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Mesh: {fileID: 0}
--- !u!33 &56
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &57
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!81 &58
AudioListener:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
--- !u!82 &59
AudioSource:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  serializedVersion: 3
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    - time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
--- !u!82 &60
AudioSource:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  serializedVersion: 3
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    - time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
--- !u!82 &61
AudioSource:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  serializedVersion: 3
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    - time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
--- !u!92 &62
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
--- !u!108 &63
Light:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Enabled: 1
  serializedVersion: 3
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: .5
  m_Range: 10
  m_SpotAngle: 30
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_Strength: 1
    m_Bias: .0500000007
    m_Softness: 4
    m_SoftnessFade: 1
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_ActuallyLightmapped: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_Lightmapping: 1
  m_ShadowSamples: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_IndirectIntensity: 1
  m_AreaSize: {x: 1, y: 1}
--- !u!124 &64
Behaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
--- !u!114 &65
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: STOP!
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 7
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: STOP!
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
    useGradient: 0
    textureGradient: 0
    anchor: 7
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &66
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7eeb7d27f0df004e90cb9fda2d821bd, type: 1}
  m_Name: 
  viewCamera: {fileID: 0}
  buttonDownSprite: button_down
  buttonUpSprite: button_up
  buttonPressedSprite: button_up
  buttonDownSound: {fileID: 0}
  buttonUpSound: {fileID: 0}
  buttonPressedSound: {fileID: 8300000, guid: 50714a94174923d42a7b31cdd432bc6c, type: 1}
  targetObject: {fileID: 22}
  messageName: StopSpinning
  targetScale: 1.10000002
  scaleTime: .0500000007
  pressedWaitTime: .300000012
--- !u!114 &67
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 0, b: 0, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 32
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &68
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 32
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &69
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7eeb7d27f0df004e90cb9fda2d821bd, type: 1}
  m_Name: 
  viewCamera: {fileID: 0}
  buttonDownSprite: button_down
  buttonUpSprite: button_up
  buttonPressedSprite: button_up
  buttonDownSound: {fileID: 0}
  buttonUpSound: {fileID: 0}
  buttonPressedSound: {fileID: 8300000, guid: 50714a94174923d42a7b31cdd432bc6c, type: 1}
  targetObject: {fileID: 22}
  messageName: SpinLeft
  targetScale: 1.10000002
  scaleTime: .0500000007
  pressedWaitTime: .300000012
--- !u!114 &70
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 16}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: SPIN LEFT
  _color: {r: 1, g: 1, b: 1, a: .549019635}
  _color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 7
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: SPIN LEFT
    color: {r: 1, g: 1, b: 1, a: .549019635}
    color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
    useGradient: 1
    textureGradient: 0
    anchor: 7
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &71
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 17}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: SPIN RIGHT
  _color: {r: 1, g: 1, b: 1, a: .549019635}
  _color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 7
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: SPIN RIGHT
    color: {r: 1, g: 1, b: 1, a: .549019635}
    color2: {r: .752941191, g: .656677902, b: .587589383, a: .847058833}
    useGradient: 1
    textureGradient: 0
    anchor: 7
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &72
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7eeb7d27f0df004e90cb9fda2d821bd, type: 1}
  m_Name: 
  viewCamera: {fileID: 0}
  buttonDownSprite: button_down
  buttonUpSprite: button_up
  buttonPressedSprite: button_up
  buttonDownSound: {fileID: 0}
  buttonUpSound: {fileID: 0}
  buttonPressedSound: {fileID: 8300000, guid: 50714a94174923d42a7b31cdd432bc6c, type: 1}
  targetObject: {fileID: 22}
  messageName: SpinRight
  targetScale: 1.10000002
  scaleTime: .0500000007
  pressedWaitTime: .300000012
--- !u!114 &73
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 32
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &74
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
  _text: Sprite & Text in 3D
  _color: {r: 1, g: .566433549, b: 0, a: 1}
  _color2: {r: 1, g: 0, b: 0, a: 1}
  _useGradient: 1
  _textureGradient: 0
  _anchor: 7
  _scale: {x: 2, y: 2, z: 2}
  _kerning: 0
  _maxChars: 19
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 44e006873ca04f741b2ac3ba5dec92ad, type: 2}
    text: Sprite & Text in 3D
    color: {r: 1, g: .566433549, b: 0, a: 1}
    color2: {r: 1, g: 0, b: 0, a: 1}
    useGradient: 1
    textureGradient: 0
    anchor: 7
    renderLayer: 0
    scale: {x: 2, y: 2, z: 2}
    kerning: 0
    maxChars: 19
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &75
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 20
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &76
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7888a80a5c0c221449a510002bb55c00, type: 1}
  m_Name: 
--- !u!196 &77
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: data
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d546f34a90531a14eaba82a43b05b86b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 3
  materialIdsValid: 1
  needMaterialInstance: 0
  spriteDefinitions:
  - name: bubble_green
    boundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.128125, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.128125, y: 0.14375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.0625, y: -0.071875, z: 0}
    - {x: 0.065625, y: -0.071875, z: 0}
    - {x: -0.0625, y: 0.071875, z: 0}
    - {x: 0.065625, y: 0.071875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.28515723}
    - {x: 0.8769551, y: 0.32519433}
    - {x: 0.9667949, y: 0.28515723}
    - {x: 0.9667949, y: 0.32519433}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 7aca923e78526cd4b984265f396e04d8
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: bubble_red
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.112500004, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.112500004, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.05, y: -0.056250002, z: 0}
    - {x: 0.05, y: -0.056250002, z: 0}
    - {x: -0.05, y: 0.056250002, z: 0}
    - {x: 0.05, y: 0.056250002, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.28906447, y: 0.96582127}
    - {x: 0.28906447, y: 0.99706936}
    - {x: 0.35937303, y: 0.96582127}
    - {x: 0.35937303, y: 0.99706936}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: bb6c9fb18b1dc46499fc0567ff873e42
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: bubble_orange
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.112500004, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.112500004, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.05, y: -0.056250002, z: 0}
    - {x: 0.05, y: -0.056250002, z: 0}
    - {x: -0.05, y: 0.056250002, z: 0}
    - {x: 0.05, y: 0.056250002, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.36718947, y: 0.96582127}
    - {x: 0.36718947, y: 0.99706936}
    - {x: 0.43749803, y: 0.96582127}
    - {x: 0.43749803, y: 0.99706936}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 4a3adedb01045d0458d6e6330d1dd25d
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: sun
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.44375, y: 0.44375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.44375, y: 0.44375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.221875, y: -0.221875, z: 0}
    - {x: 0.221875, y: -0.221875, z: 0}
    - {x: -0.221875, y: 0.221875, z: 0}
    - {x: 0.221875, y: 0.221875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.85449314}
    - {x: 0.28124803, y: 0.85449314}
    - {x: 0.003908203, y: 0.9931631}
    - {x: 0.28124803, y: 0.9931631}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 60ab1d7b85ae054498eb9122996758d9
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: bg
    boundsData:
    - {x: 0.015625, y: -0.015625, z: 0}
    - {x: 3.21875, y: 2.15625, z: 0}
    untrimmedBoundsData:
    - {x: 0.015625, y: -0.015625, z: 0}
    - {x: 3.21875, y: 2.15625, z: 0}
    texelSize: {x: 0.03125, y: 0.03125}
    positions:
    - {x: -1.59375, y: -1.09375, z: 0}
    - {x: 1.625, y: -1.09375, z: 0}
    - {x: -1.59375, y: 1.0625, z: 0}
    - {x: 1.625, y: 1.0625, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.40234473}
    - {x: 0.5292949, y: 0.40234473}
    - {x: 0.32812697, y: 0.46972558}
    - {x: 0.5292949, y: 0.46972558}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: b5e1233ff1ab3d14a8b3e3910c4c8281
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke0
    boundsData:
    - {x: 0.0015624985, y: -0.0015624985, z: 0}
    - {x: 0.140625, y: 0.140625, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624985, y: -0.0015624985, z: 0}
    - {x: 0.140625, y: 0.140625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.06875, y: -0.071875, z: 0}
    - {x: 0.071875, y: -0.071875, z: 0}
    - {x: -0.06875, y: 0.06875, z: 0}
    - {x: 0.071875, y: 0.06875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.71093947, y: 0.9541025}
    - {x: 0.79882616, y: 0.9541025}
    - {x: 0.71093947, y: 0.9980459}
    - {x: 0.79882616, y: 0.9980459}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: c9b9253339e73124bbe3b5012f4bc936
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke4
    boundsData:
    - {x: 0.001562506, y: 0, z: 0}
    - {x: 0.253125, y: 0.22500001, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: 0, z: 0}
    - {x: 0.253125, y: 0.22500001, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.125, y: -0.112500004, z: 0}
    - {x: 0.12812501, y: -0.112500004, z: 0}
    - {x: -0.125, y: 0.112500004, z: 0}
    - {x: 0.12812501, y: 0.112500004, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.56250197, y: 0.11230566}
    - {x: 0.56250197, y: 0.19140527}
    - {x: 0.70312303, y: 0.11230566}
    - {x: 0.70312303, y: 0.19140527}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 39734a83ab620ae42b7fd778891e490e
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke2
    boundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.221875, y: 0.22500001, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.221875, y: 0.22500001, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.109375, y: -0.112500004, z: 0}
    - {x: 0.1125, y: -0.112500004, z: 0}
    - {x: -0.109375, y: 0.112500004, z: 0}
    - {x: 0.1125, y: 0.112500004, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.55273634, y: 0.19531348}
    - {x: 0.55273634, y: 0.26464745}
    - {x: 0.6933574, y: 0.19531348}
    - {x: 0.6933574, y: 0.26464745}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 287c70551d47fed40aaed1d763b2c586
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke3
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.26250002, y: 0.26250002, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.26250002, y: 0.26250002, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.13125001, y: -0.13125001, z: 0}
    - {x: 0.13125001, y: -0.13125001, z: 0}
    - {x: -0.13125001, y: 0.13125001, z: 0}
    - {x: 0.13125001, y: 0.13125001, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.36718947, y: 0.2832041}
    - {x: 0.53124803, y: 0.2832041}
    - {x: 0.36718947, y: 0.3652334}
    - {x: 0.53124803, y: 0.3652334}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: f54a71b7ee1fcd541859ccce61c15d4f
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke1
    boundsData:
    - {x: 0.001562506, y: -0.0015624985, z: 0}
    - {x: 0.20937501, y: 0.20937501, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.0015624985, z: 0}
    - {x: 0.20937501, y: 0.20937501, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.103125, y: -0.10625, z: 0}
    - {x: 0.10625001, y: -0.10625, z: 0}
    - {x: -0.103125, y: 0.103125006, z: 0}
    - {x: 0.10625001, y: 0.103125006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.53711134, y: 0.41308692}
    - {x: 0.6679668, y: 0.41308692}
    - {x: 0.53711134, y: 0.47851464}
    - {x: 0.6679668, y: 0.47851464}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: e89d30fcbd3b3cb4f81b9f612565cd15
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: explosion_beam
    boundsData:
    - {x: 0.0015625022, y: -0.0031250045, z: 0}
    - {x: 0.090625, y: 0.41250002, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015625022, y: -0.001562506, z: 0}
    - {x: 0.090625, y: 0.421875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.04375, y: -0.20937501, z: 0}
    - {x: 0.046875004, y: -0.20937501, z: 0}
    - {x: -0.04375, y: 0.203125, z: 0}
    - {x: 0.046875004, y: 0.203125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.44531447, y: 0.96582127}
    - {x: 0.44531447, y: 0.9941397}
    - {x: 0.70312303, y: 0.96582127}
    - {x: 0.70312303, y: 0.9941397}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 972d12151f1a30d4fb5c0457807734e9
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke
    boundsData:
    - {x: 0, y: -0.0015625004, z: 0}
    - {x: 0.01875, y: 0.021875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: -0.0015625004, z: 0}
    - {x: 0.01875, y: 0.021875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.009375, y: -0.0125, z: 0}
    - {x: 0.009375, y: -0.0125, z: 0}
    - {x: -0.009375, y: 0.009374999, z: 0}
    - {x: 0.009375, y: 0.009374999, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.70117384, y: 0.19531348}
    - {x: 0.71288866, y: 0.19531348}
    - {x: 0.70117384, y: 0.20214745}
    - {x: 0.71288866, y: 0.20214745}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 6109dd1f0c6a20e40b9a5cc3cd4bbfaa
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: core
    boundsData:
    - {x: -0.0015624985, y: -0.0031249896, z: 0}
    - {x: 0.38437504, y: 0.3625, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: -0.0015624911, z: 0}
    - {x: 0.39375, y: 0.37187502, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.19375001, y: -0.184375, z: 0}
    - {x: 0.19062501, y: -0.184375, z: 0}
    - {x: -0.19375001, y: 0.17812502, z: 0}
    - {x: 0.19062501, y: 0.17812502, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.28906447, y: 0.8417978}
    - {x: 0.28906447, y: 0.9619131}
    - {x: 0.51562303, y: 0.8417978}
    - {x: 0.51562303, y: 0.9619131}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 41539258830f7a24abc38d8bbe487af3
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: bg1
    boundsData:
    - {x: 0, y: 0.253125, z: 0}
    - {x: 3.2, y: 0.50625, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0.253125, z: 0}
    - {x: 3.2, y: 0.50625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -1.6, y: 0, z: 0}
    - {x: -1.4, y: 0, z: 0}
    - {x: -1.6, y: 0.49062502, z: 0}
    - {x: -1.4, y: 0.49062502, z: 0}
    - {x: -1.4, y: 0, z: 0}
    - {x: -1.2, y: 0, z: 0}
    - {x: -1.4, y: 0.503125, z: 0}
    - {x: -1.2, y: 0.503125, z: 0}
    - {x: -1.2, y: 0, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: -1.2, y: 0.503125, z: 0}
    - {x: -1, y: 0.503125, z: 0}
    - {x: -1, y: 0, z: 0}
    - {x: -0.8, y: 0, z: 0}
    - {x: -1, y: 0.5, z: 0}
    - {x: -0.8, y: 0.5, z: 0}
    - {x: -0.8, y: 0, z: 0}
    - {x: -0.6, y: 0, z: 0}
    - {x: -0.8, y: 0.48437503, z: 0}
    - {x: -0.6, y: 0.48437503, z: 0}
    - {x: -0.6, y: 0, z: 0}
    - {x: -0.4, y: 0, z: 0}
    - {x: -0.6, y: 0.46562502, z: 0}
    - {x: -0.4, y: 0.46562502, z: 0}
    - {x: -0.4, y: 0, z: 0}
    - {x: -0.2, y: 0, z: 0}
    - {x: -0.4, y: 0.44375002, z: 0}
    - {x: -0.2, y: 0.44375002, z: 0}
    - {x: -0.2, y: 0, z: 0}
    - {x: 0, y: 0, z: 0}
    - {x: -0.2, y: 0.428125, z: 0}
    - {x: 0, y: 0.428125, z: 0}
    - {x: 0, y: 0, z: 0}
    - {x: 0.2, y: 0, z: 0}
    - {x: 0, y: 0.45625, z: 0}
    - {x: 0.2, y: 0.45625, z: 0}
    - {x: 0.2, y: 0, z: 0}
    - {x: 0.4, y: 0, z: 0}
    - {x: 0.2, y: 0.47500002, z: 0}
    - {x: 0.4, y: 0.47500002, z: 0}
    - {x: 0.4, y: 0, z: 0}
    - {x: 0.6, y: 0, z: 0}
    - {x: 0.4, y: 0.49062502, z: 0}
    - {x: 0.6, y: 0.49062502, z: 0}
    - {x: 0.6, y: 0, z: 0}
    - {x: 0.8, y: 0, z: 0}
    - {x: 0.6, y: 0.503125, z: 0}
    - {x: 0.8, y: 0.503125, z: 0}
    - {x: 0.8, y: 0, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 0.8, y: 0.50625, z: 0}
    - {x: 1, y: 0.50625, z: 0}
    - {x: 1, y: 0, z: 0}
    - {x: 1.2, y: 0, z: 0}
    - {x: 1, y: 0.50625, z: 0}
    - {x: 1.2, y: 0.50625, z: 0}
    - {x: 1.2, y: 0, z: 0}
    - {x: 1.4, y: 0, z: 0}
    - {x: 1.2, y: 0.503125, z: 0}
    - {x: 1.4, y: 0.503125, z: 0}
    - {x: 1.4, y: 0, z: 0}
    - {x: 1.6, y: 0, z: 0}
    - {x: 1.4, y: 0.49375004, z: 0}
    - {x: 1.6, y: 0.49375004, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.6132822}
    - {x: 0.003908203, y: 0.6757803}
    - {x: 0.3105449, y: 0.6132822}
    - {x: 0.3105449, y: 0.6757803}
    - {x: 0.003908203, y: 0.41406348}
    - {x: 0.003908203, y: 0.47656152}
    - {x: 0.3183574, y: 0.41406348}
    - {x: 0.3183574, y: 0.47656152}
    - {x: 0.003908203, y: 0.48046973}
    - {x: 0.003908203, y: 0.5429678}
    - {x: 0.3183574, y: 0.48046973}
    - {x: 0.3183574, y: 0.5429678}
    - {x: 0.6679707, y: 0.51562595}
    - {x: 0.6679707, y: 0.57812405}
    - {x: 0.9804668, y: 0.51562595}
    - {x: 0.9804668, y: 0.57812405}
    - {x: 0.66406447, y: 0.5820322}
    - {x: 0.66406447, y: 0.6445303}
    - {x: 0.9667949, y: 0.5820322}
    - {x: 0.9667949, y: 0.6445303}
    - {x: 0.66406447, y: 0.64843845}
    - {x: 0.66406447, y: 0.71093655}
    - {x: 0.95507616, y: 0.64843845}
    - {x: 0.95507616, y: 0.71093655}
    - {x: 0.003908203, y: 0.7460947}
    - {x: 0.003908203, y: 0.8085928}
    - {x: 0.28124803, y: 0.7460947}
    - {x: 0.28124803, y: 0.8085928}
    - {x: 0.71093947, y: 0.7148447}
    - {x: 0.71093947, y: 0.7773428}
    - {x: 0.97851366, y: 0.7148447}
    - {x: 0.97851366, y: 0.7773428}
    - {x: 0.53125197, y: 0.70800877}
    - {x: 0.65624803, y: 0.70800877}
    - {x: 0.53125197, y: 0.850585}
    - {x: 0.65624803, y: 0.850585}
    - {x: 0.6992207, y: 0.38281348}
    - {x: 0.6992207, y: 0.44531152}
    - {x: 0.9960918, y: 0.38281348}
    - {x: 0.9960918, y: 0.44531152}
    - {x: 0.003908203, y: 0.67968845}
    - {x: 0.003908203, y: 0.74218655}
    - {x: 0.3105449, y: 0.67968845}
    - {x: 0.3105449, y: 0.74218655}
    - {x: 0.003908203, y: 0.54687595}
    - {x: 0.003908203, y: 0.60937405}
    - {x: 0.3183574, y: 0.54687595}
    - {x: 0.3183574, y: 0.60937405}
    - {x: 0.003908203, y: 0.28125098}
    - {x: 0.003908203, y: 0.34374902}
    - {x: 0.32031053, y: 0.28125098}
    - {x: 0.32031053, y: 0.34374902}
    - {x: 0.003908203, y: 0.34765723}
    - {x: 0.003908203, y: 0.41015527}
    - {x: 0.32031053, y: 0.34765723}
    - {x: 0.32031053, y: 0.41015527}
    - {x: 0.6757832, y: 0.44921973}
    - {x: 0.6757832, y: 0.5117178}
    - {x: 0.9902324, y: 0.44921973}
    - {x: 0.9902324, y: 0.5117178}
    - {x: 0.53125197, y: 0.54980564}
    - {x: 0.65624803, y: 0.54980564}
    - {x: 0.53125197, y: 0.7041006}
    - {x: 0.65624803, y: 0.7041006}
    normalizedUvs:
    - {x: 0, y: 0.5831923}
    - {x: 0, y: 0.69296634}
    - {x: 0.30905238, y: 0.5831923}
    - {x: 0.30905238, y: 0.69296634}
    - {x: 0, y: 0.23327693}
    - {x: 0, y: 0.34305087}
    - {x: 0.31692645, y: 0.23327693}
    - {x: 0.31692645, y: 0.34305087}
    - {x: 0, y: 0.34991542}
    - {x: 0, y: 0.45968938}
    - {x: 0.31692645, y: 0.34991542}
    - {x: 0.31692645, y: 0.45968938}
    - {x: 0.669294, y: 0.41166514}
    - {x: 0.669294, y: 0.5214392}
    - {x: 0.9842519, y: 0.41166514}
    - {x: 0.9842519, y: 0.5214392}
    - {x: 0.665357, y: 0.5283036}
    - {x: 0.665357, y: 0.6380776}
    - {x: 0.97047234, y: 0.5283036}
    - {x: 0.97047234, y: 0.6380776}
    - {x: 0.665357, y: 0.64494205}
    - {x: 0.665357, y: 0.7547161}
    - {x: 0.95866126, y: 0.64494205}
    - {x: 0.95866126, y: 0.7547161}
    - {x: 0, y: 0.81646925}
    - {x: 0, y: 0.92624325}
    - {x: 0.2795247, y: 0.81646925}
    - {x: 0.2795247, y: 0.92624325}
    - {x: 0.71260124, y: 0.7615805}
    - {x: 0.71260124, y: 0.8713546}
    - {x: 0.9822834, y: 0.7615805}
    - {x: 0.9822834, y: 0.8713546}
    - {x: 0.5314982, y: 0.74957365}
    - {x: 0.657479, y: 0.74957365}
    - {x: 0.5314982, y: 0.99999994}
    - {x: 0.657479, y: 0.99999994}
    - {x: 0.70079017, y: 0.17838825}
    - {x: 0.70079017, y: 0.28816217}
    - {x: 1, y: 0.17838825}
    - {x: 1, y: 0.28816217}
    - {x: 0, y: 0.6998308}
    - {x: 0, y: 0.8096048}
    - {x: 0.30905238, y: 0.6998308}
    - {x: 0.30905238, y: 0.8096048}
    - {x: 0, y: 0.46655384}
    - {x: 0, y: 0.57632786}
    - {x: 0.31692645, y: 0.46655384}
    - {x: 0.31692645, y: 0.57632786}
    - {x: 0, y: 0}
    - {x: 0, y: 0.10977393}
    - {x: 0.31889495, y: 0}
    - {x: 0.31889495, y: 0.10977393}
    - {x: 0, y: 0.11663847}
    - {x: 0, y: 0.2264124}
    - {x: 0.31889495, y: 0.11663847}
    - {x: 0.31889495, y: 0.2264124}
    - {x: 0.6771681, y: 0.29502672}
    - {x: 0.6771681, y: 0.40480068}
    - {x: 0.9940945, y: 0.29502672}
    - {x: 0.9940945, y: 0.40480068}
    - {x: 0.5314982, y: 0.47169966}
    - {x: 0.657479, y: 0.47169966}
    - {x: 0.5314982, y: 0.7427092}
    - {x: 0.657479, y: 0.7427092}
    indices: 000000000300000001000000020000000300000000000000040000000700000005000000060000000700000004000000080000000b000000090000000a0000000b000000080000000c0000000f0000000d0000000e0000000f0000000c000000100000001300000011000000120000001300000010000000140000001700000015000000160000001700000014000000180000001b000000190000001a0000001b000000180000001c0000001f0000001d0000001e0000001f0000001c000000200000002300000021000000220000002300000020000000240000002700000025000000260000002700000024000000280000002b000000290000002a0000002b000000280000002c0000002f0000002d0000002e0000002f0000002c000000300000003300000031000000320000003300000030000000340000003700000035000000360000003700000034000000380000003b000000390000003a0000003b000000380000003c0000003f0000003d0000003e0000003f0000003c000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 0d86fdb28836c6445974f98f412dfbf2
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: sheep_0
    boundsData:
    - {x: 0.0046874993, y: 0.0234375, z: 0}
    - {x: 0.071875, y: 0.053125, z: 0}
    untrimmedBoundsData:
    - {x: 0.0046874993, y: 0.0234375, z: 0}
    - {x: 0.071875, y: 0.053125, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.03125, y: -0.003125, z: 0}
    - {x: 0.040625, y: -0.003125, z: 0}
    - {x: -0.03125, y: 0.05, z: 0}
    - {x: 0.040625, y: 0.05, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.6992207, y: 0.36035255}
    - {x: 0.74413866, y: 0.36035255}
    - {x: 0.6992207, y: 0.37695214}
    - {x: 0.74413866, y: 0.37695214}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: c25d0635778cf614eb2cca727e071fd6
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: sheep_1
    boundsData:
    - {x: 0.0062499987, y: 0.0234375, z: 0}
    - {x: 0.06875, y: 0.053125, z: 0}
    untrimmedBoundsData:
    - {x: 0.0062499987, y: 0.0234375, z: 0}
    - {x: 0.06875, y: 0.053125, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.028125001, y: -0.003125, z: 0}
    - {x: 0.040625, y: -0.003125, z: 0}
    - {x: -0.028125001, y: 0.05, z: 0}
    - {x: 0.040625, y: 0.05, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7519551, y: 0.36035255}
    - {x: 0.7949199, y: 0.36035255}
    - {x: 0.7519551, y: 0.37695214}
    - {x: 0.7949199, y: 0.37695214}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: d20c61a95bed34948a4408be780c3d55
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: sheep_2
    boundsData:
    - {x: 0.007812503, y: 0.0234375, z: 0}
    - {x: 0.065625004, y: 0.053125, z: 0}
    untrimmedBoundsData:
    - {x: 0.007812503, y: 0.0234375, z: 0}
    - {x: 0.065625004, y: 0.053125, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.003125, z: 0}
    - {x: 0.040625006, y: -0.003125, z: 0}
    - {x: -0.025, y: 0.05, z: 0}
    - {x: 0.040625006, y: 0.05, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.80273634, y: 0.36035255}
    - {x: 0.84374803, y: 0.36035255}
    - {x: 0.80273634, y: 0.37695214}
    - {x: 0.84374803, y: 0.37695214}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: df162a39ace0bbb4dbbe4c348669075c
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: cloud1
    boundsData:
    - {x: 0, y: -0.001562506, z: 0}
    - {x: 0.45000002, y: 0.221875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: -0.001562506, z: 0}
    - {x: 0.45000002, y: 0.221875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.22500001, y: -0.112500004, z: 0}
    - {x: 0.22500001, y: -0.112500004, z: 0}
    - {x: -0.22500001, y: 0.10937499, z: 0}
    - {x: 0.22500001, y: 0.10937499, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.55273634, y: 0.26855567}
    - {x: 0.55273634, y: 0.4091787}
    - {x: 0.6914043, y: 0.26855567}
    - {x: 0.6914043, y: 0.4091787}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 4a902aa2ec2deaa469755f61aa3e8134
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: cloud2
    boundsData:
    - {x: 0, y: -0.0015624911, z: 0}
    - {x: 0.56875, y: 0.27812502, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: -0.0015624911, z: 0}
    - {x: 0.56875, y: 0.27812502, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.284375, y: -0.140625, z: 0}
    - {x: 0.284375, y: -0.140625, z: 0}
    - {x: -0.284375, y: 0.13750002, z: 0}
    - {x: 0.284375, y: 0.13750002, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.19043067}
    - {x: 0.35937303, y: 0.19043067}
    - {x: 0.003908203, y: 0.27734277}
    - {x: 0.35937303, y: 0.27734277}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: a329c3266c887ba44b0bf7c627043688
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: Brown Block
    boundsData:
    - {x: 0.001562506, y: -0.07968751, z: 0}
    - {x: 0.315625, y: 0.378125, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.001562506, z: 0}
    - {x: 0.315625, y: 0.534375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.15625, y: -0.26875, z: 0}
    - {x: 0.15937501, y: -0.26875, z: 0}
    - {x: -0.15625, y: 0.109374985, z: 0}
    - {x: 0.15937501, y: 0.109374985, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32617384, y: 0.5976572}
    - {x: 0.52343553, y: 0.5976572}
    - {x: 0.32617384, y: 0.71581936}
    - {x: 0.52343553, y: 0.71581936}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 9fc5fcd3fc8803f4caf11d337579f6c8
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: Chest Closed
    boundsData:
    - {x: 0.001562506, y: -0.07968751, z: 0}
    - {x: 0.315625, y: 0.378125, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.001562506, z: 0}
    - {x: 0.315625, y: 0.534375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.15625, y: -0.26875, z: 0}
    - {x: 0.15937501, y: -0.26875, z: 0}
    - {x: -0.15625, y: 0.109374985, z: 0}
    - {x: 0.15937501, y: 0.109374985, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.31836134, y: 0.7197275}
    - {x: 0.51562303, y: 0.7197275}
    - {x: 0.31836134, y: 0.8378897}
    - {x: 0.51562303, y: 0.8378897}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 6b3b26a1fb4ebcf448d47859ec1cb66f
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: Stone Block
    boundsData:
    - {x: 0.001562506, y: -0.07656251, z: 0}
    - {x: 0.315625, y: 0.384375, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.001562506, z: 0}
    - {x: 0.315625, y: 0.534375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.15625, y: -0.26875, z: 0}
    - {x: 0.15937501, y: -0.26875, z: 0}
    - {x: -0.15625, y: 0.115624994, z: 0}
    - {x: 0.15937501, y: 0.115624994, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32617384, y: 0.4736338}
    - {x: 0.52343553, y: 0.4736338}
    - {x: 0.32617384, y: 0.59374905}
    - {x: 0.52343553, y: 0.59374905}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 2deb4cd15b4f4144894c8b9f4f1a19a3
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: Rock
    boundsData:
    - {x: 0.0031250045, y: -0.093750015, z: 0}
    - {x: 0.30625004, y: 0.30625, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.001562506, z: 0}
    - {x: 0.315625, y: 0.534375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.15, y: -0.24687502, z: 0}
    - {x: 0.15625001, y: -0.24687502, z: 0}
    - {x: -0.15, y: 0.059374984, z: 0}
    - {x: 0.15625001, y: 0.059374984, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.52343947, y: 0.85449314}
    - {x: 0.7148418, y: 0.85449314}
    - {x: 0.52343947, y: 0.95019436}
    - {x: 0.7148418, y: 0.95019436}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 82f0ed3ac7232ae4c8b948fb7654f205
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: crate
    boundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.203125, y: 0.20625, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.203125, y: 0.20625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.1, y: -0.103125, z: 0}
    - {x: 0.103125, y: -0.103125, z: 0}
    - {x: -0.1, y: 0.103125, z: 0}
    - {x: 0.103125, y: 0.103125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.53125197, y: 0.48242286}
    - {x: 0.53125197, y: 0.5458975}
    - {x: 0.6601543, y: 0.48242286}
    - {x: 0.6601543, y: 0.5458975}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: f6724743a506f464ea1684044c461aef
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 2
    customColliders: []
    colliderVertices:
    - {x: 0.0015624985, y: 0, z: 0}
    - {x: 0.1015625, y: 0.103125, z: 0.1}
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: ground
    boundsData:
    - {x: 0.0015624762, y: 0, z: 0}
    - {x: 1.134375, y: 0.0875, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624762, y: 0, z: 0}
    - {x: 1.134375, y: 0.0875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.565625, y: -0.04375, z: 0}
    - {x: 0.56874996, y: -0.04375, z: 0}
    - {x: -0.565625, y: 0.04375, z: 0}
    - {x: 0.56874996, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.08105566}
    - {x: 0.71288866, y: 0.08105566}
    - {x: 0.003908203, y: 0.10839746}
    - {x: 0.71288866, y: 0.10839746}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 2f1986f4c79909a4ca592f02dec914e4
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: smoke5
    boundsData:
    - {x: 0.0015625, y: 0.0015625, z: 0}
    - {x: 0.003125, y: 0.003125, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.00625, y: 0.00625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: 0, y: 0, z: 0}
    - {x: 0.003125, y: 0, z: 0}
    - {x: 0, y: 0.003125, z: 0}
    - {x: 0.003125, y: 0.003125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.99804884, y: 0.0019541015}
    - {x: 0.99218553, y: 0.0019541015}
    - {x: 0.99804884, y: -0.0009775391}
    - {x: 0.99218553, y: -0.0009775391}
    normalizedUvs:
    - {x: 1, y: 1}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 0, y: 0}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 3fe04aba96e31d748a88af0fdc870d13
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: animdemo_4
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.084375, y: -0.084375, z: 0}
    - {x: 0.084375, y: -0.084375, z: 0}
    - {x: -0.084375, y: 0.084375, z: 0}
    - {x: 0.084375, y: 0.084375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8847676, y: 0.0019541015}
    - {x: 0.9902324, y: 0.0019541015}
    - {x: 0.8847676, y: 0.054686524}
    - {x: 0.9902324, y: 0.054686524}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 27e59c976f45f48488bd746d93f38085
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: animdemo_5
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.084375, y: -0.084375, z: 0}
    - {x: 0.084375, y: -0.084375, z: 0}
    - {x: -0.084375, y: 0.084375, z: 0}
    - {x: 0.084375, y: 0.084375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8847676, y: 0.058594726}
    - {x: 0.9902324, y: 0.058594726}
    - {x: 0.8847676, y: 0.11132715}
    - {x: 0.9902324, y: 0.11132715}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: ce42d39374f446b43974cdf748be1594
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: animdemo_1
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.084375, y: -0.084375, z: 0}
    - {x: 0.084375, y: -0.084375, z: 0}
    - {x: -0.084375, y: 0.084375, z: 0}
    - {x: 0.084375, y: 0.084375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.11523535}
    - {x: 0.9824199, y: 0.11523535}
    - {x: 0.8769551, y: 0.16796777}
    - {x: 0.9824199, y: 0.16796777}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 7a1c114efdc6f1548b40b62b7666cf2e
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: animdemo_3
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.084375, y: -0.084375, z: 0}
    - {x: 0.084375, y: -0.084375, z: 0}
    - {x: -0.084375, y: 0.084375, z: 0}
    - {x: 0.084375, y: 0.084375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.17187598}
    - {x: 0.9824199, y: 0.17187598}
    - {x: 0.8769551, y: 0.22460839}
    - {x: 0.9824199, y: 0.22460839}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: caa05169d2cbf6b4d8d7748e71781a40
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: animdemo_2
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.16875, y: 0.16875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.084375, y: -0.084375, z: 0}
    - {x: 0.084375, y: -0.084375, z: 0}
    - {x: -0.084375, y: 0.084375, z: 0}
    - {x: 0.084375, y: 0.084375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.22851661}
    - {x: 0.9824199, y: 0.22851661}
    - {x: 0.8769551, y: 0.28124902}
    - {x: 0.9824199, y: 0.28124902}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: e84a1e8c2679c6347b789eb93ff1893f
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: button_down
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.88125, y: 0.2375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.88125, y: 0.2375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.440625, y: -0.11875, z: 0}
    - {x: 0.440625, y: -0.11875, z: 0}
    - {x: -0.440625, y: 0.11875, z: 0}
    - {x: 0.440625, y: 0.11875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7207051, y: 0.08105566}
    - {x: 0.7207051, y: 0.35644433}
    - {x: 0.86913866, y: 0.08105566}
    - {x: 0.86913866, y: 0.35644433}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: f31ec590f22df524e97e5d2f9139ec46
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: button_up
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.88125, y: 0.2375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.88125, y: 0.2375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.440625, y: -0.11875, z: 0}
    - {x: 0.440625, y: -0.11875, z: 0}
    - {x: -0.440625, y: 0.11875, z: 0}
    - {x: 0.440625, y: 0.11875, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.11230566}
    - {x: 0.55468553, y: 0.11230566}
    - {x: 0.003908203, y: 0.18652245}
    - {x: 0.55468553, y: 0.18652245}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: a6bc359a914e1df468afe824439ede88
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: wavyground
    boundsData:
    - {x: 0.0015624762, y: -0.0015624985, z: 0}
    - {x: 1.396875, y: 0.24062501, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624762, y: -0.0015624985, z: 0}
    - {x: 1.396875, y: 0.24062501, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.69687504, y: -0.121875, z: 0}
    - {x: 0.7, y: -0.121875, z: 0}
    - {x: -0.69687504, y: 0.118750006, z: 0}
    - {x: 0.7, y: 0.118750006, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.0019541015}
    - {x: 0.87695116, y: 0.0019541015}
    - {x: 0.003908203, y: 0.07714746}
    - {x: 0.87695116, y: 0.07714746}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: abd2d4d8e662ace4c961589c5d18e01f
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 3
    customColliders: []
    colliderVertices:
    - {x: 0.5125, y: 0.021874994, z: -0.1}
    - {x: 0.5125, y: 0.021874994, z: 0.1}
    - {x: 0.340625, y: 0, z: -0.1}
    - {x: 0.340625, y: 0, z: 0.1}
    - {x: 0.27187496, y: 0, z: -0.1}
    - {x: 0.27187496, y: 0, z: 0.1}
    - {x: 0.16874999, y: 0.040625006, z: -0.1}
    - {x: 0.16874999, y: 0.040625006, z: 0.1}
    - {x: 0.115624964, y: 0.0625, z: -0.1}
    - {x: 0.115624964, y: 0.0625, z: 0.1}
    - {x: 0.06562495, y: 0.068749994, z: -0.1}
    - {x: 0.06562495, y: 0.068749994, z: 0.1}
    - {x: -0.018750012, y: 0.049999997, z: -0.1}
    - {x: -0.018750012, y: 0.049999997, z: 0.1}
    - {x: -0.100000024, y: 0.025000006, z: -0.1}
    - {x: -0.100000024, y: 0.025000006, z: 0.1}
    - {x: -0.1875, y: 0.009375006, z: -0.1}
    - {x: -0.1875, y: 0.009375006, z: 0.1}
    - {x: -0.25312504, y: 0.025000006, z: -0.1}
    - {x: -0.25312504, y: 0.025000006, z: 0.1}
    - {x: -0.32187504, y: 0.056250006, z: -0.1}
    - {x: -0.32187504, y: 0.056250006, z: 0.1}
    - {x: -0.38750002, y: 0.084374994, z: -0.1}
    - {x: -0.38750002, y: 0.084374994, z: 0.1}
    - {x: -0.45625, y: 0.096875, z: -0.1}
    - {x: -0.45625, y: 0.096875, z: 0.1}
    - {x: -0.584375, y: 0.065625, z: -0.1}
    - {x: -0.584375, y: 0.065625, z: 0.1}
    - {x: -0.69687504, y: 0.015625, z: -0.1}
    - {x: -0.69687504, y: 0.015625, z: 0.1}
    - {x: -0.69687504, y: -0.121875, z: -0.1}
    - {x: -0.69687504, y: -0.121875, z: 0.1}
    - {x: 0.7, y: -0.121875, z: -0.1}
    - {x: 0.7, y: -0.121875, z: 0.1}
    - {x: 0.7, y: 0.03125, z: -0.1}
    - {x: 0.7, y: 0.03125, z: 0.1}
    colliderIndicesFwd: 0200000003000000000000000000000003000000010000000400000005000000020000000200000005000000030000000600000007000000040000000400000007000000050000000800000009000000060000000600000009000000070000000a0000000b00000008000000080000000b000000090000000c0000000d0000000a0000000a0000000d0000000b0000000e0000000f0000000c0000000c0000000f0000000d00000010000000110000000e0000000e000000110000000f0000001200000013000000100000001000000013000000110000001400000015000000120000001200000015000000130000001600000017000000140000001400000017000000150000001800000019000000160000001600000019000000170000001a0000001b00000018000000180000001b000000190000001c0000001d0000001a0000001a0000001d0000001b0000001e0000001f0000001c0000001c0000001f0000001d000000200000001f0000001e00000020000000210000001f000000220000002100000020000000220000002300000021000000000000000100000022000000220000000100000023000000
    colliderIndicesBack: 000000000300000002000000010000000300000000000000020000000500000004000000030000000500000002000000040000000700000006000000050000000700000004000000060000000900000008000000070000000900000006000000080000000b0000000a000000090000000b000000080000000a0000000d0000000c0000000b0000000d0000000a0000000c0000000f0000000e0000000d0000000f0000000c0000000e00000011000000100000000f000000110000000e000000100000001300000012000000110000001300000010000000120000001500000014000000130000001500000012000000140000001700000016000000150000001700000014000000160000001900000018000000170000001900000016000000180000001b0000001a000000190000001b000000180000001a0000001d0000001c0000001b0000001d0000001a0000001c0000001f0000001e0000001d0000001f0000001c0000001e0000001f000000200000001f0000002100000020000000200000002100000022000000210000002300000022000000220000000100000000000000230000000100000022000000
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D:
    - points:
      - {x: 0.5125, y: 0.021874994}
      - {x: 0.340625, y: 0}
      - {x: 0.27187496, y: 0}
      - {x: 0.16874999, y: 0.040625006}
      - {x: 0.115624964, y: 0.0625}
      - {x: 0.06562495, y: 0.068749994}
      - {x: -0.018750012, y: 0.049999997}
      - {x: -0.100000024, y: 0.025000006}
      - {x: -0.1875, y: 0.009375006}
      - {x: -0.25312504, y: 0.025000006}
      - {x: -0.32187504, y: 0.056250006}
      - {x: -0.38750002, y: 0.084374994}
      - {x: -0.45625, y: 0.096875}
      - {x: -0.584375, y: 0.065625}
      - {x: -0.69687504, y: 0.015625}
      - {x: -0.69687504, y: -0.121875}
      - {x: 0.7, y: -0.121875}
      - {x: 0.7, y: 0.03125}
    edgeCollider2D: []
    attachPoints: []
  - name: starspike
    boundsData:
    - {x: 0.001562506, y: -0.0015624911, z: 0}
    - {x: 0.284375, y: 0.284375, z: 0}
    untrimmedBoundsData:
    - {x: 0.001562506, y: -0.0015624911, z: 0}
    - {x: 0.284375, y: 0.284375, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.140625, y: -0.14375, z: 0}
    - {x: 0.14375001, y: -0.14375, z: 0}
    - {x: -0.140625, y: 0.14062501, z: 0}
    - {x: 0.14375001, y: 0.14062501, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.36718947, y: 0.19043067}
    - {x: 0.5449199, y: 0.19043067}
    - {x: 0.36718947, y: 0.2792959}
    - {x: 0.5449199, y: 0.2792959}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fe18dfa9012243c4ea82497709a18b26
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 3
    customColliders: []
    colliderVertices:
    - {x: 0.003124997, y: 0.015625, z: -0.1}
    - {x: 0.003124997, y: 0.015625, z: 0.1}
    - {x: -0.015625, y: 0.14062501, z: -0.1}
    - {x: -0.015625, y: 0.14062501, z: 0.1}
    - {x: -0.015625, y: -0.006249994, z: -0.1}
    - {x: -0.015625, y: -0.006249994, z: 0.1}
    - {x: -0.1375, y: 0.015625, z: -0.1}
    - {x: -0.1375, y: 0.015625, z: 0.1}
    - {x: -0.003124997, y: -0.031249993, z: -0.1}
    - {x: -0.003124997, y: -0.031249993, z: 0.1}
    - {x: -0.05625, y: -0.140625, z: -0.1}
    - {x: -0.05625, y: -0.140625, z: 0.1}
    - {x: 0.028125003, y: -0.024999999, z: -0.1}
    - {x: 0.028125003, y: -0.024999999, z: 0.1}
    - {x: 0.118750006, y: -0.115624994, z: -0.1}
    - {x: 0.118750006, y: -0.115624994, z: 0.1}
    - {x: 0.03125, y: 0.003125012, z: -0.1}
    - {x: 0.03125, y: 0.003125012, z: 0.1}
    - {x: 0.14375001, y: 0.059375003, z: -0.1}
    - {x: 0.14375001, y: 0.059375003, z: 0.1}
    colliderIndicesFwd: 0200000001000000000000000200000003000000010000000400000005000000020000000200000005000000030000000600000007000000040000000400000007000000050000000800000007000000060000000800000009000000070000000a0000000b00000008000000080000000b000000090000000c0000000b0000000a0000000c0000000d0000000b0000000e0000000d0000000c0000000e0000000f0000000d000000100000000f0000000e00000010000000110000000f000000120000001100000010000000120000001300000011000000000000000100000012000000120000000100000013000000
    colliderIndicesBack: 000000000100000002000000010000000300000002000000020000000500000004000000030000000500000002000000040000000700000006000000050000000700000004000000060000000700000008000000070000000900000008000000080000000b0000000a000000090000000b000000080000000a0000000b0000000c0000000b0000000d0000000c0000000c0000000d0000000e0000000d0000000f0000000e0000000e0000000f000000100000000f0000001100000010000000100000001100000012000000110000001300000012000000120000000100000000000000130000000100000012000000
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D:
    - points:
      - {x: 0.003124997, y: 0.015625}
      - {x: -0.015625, y: 0.14062501}
      - {x: -0.015625, y: -0.006249994}
      - {x: -0.1375, y: 0.015625}
      - {x: -0.003124997, y: -0.031249993}
      - {x: -0.05625, y: -0.140625}
      - {x: 0.028125003, y: -0.024999999}
      - {x: 0.118750006, y: -0.115624994}
      - {x: 0.03125, y: 0.003125012}
      - {x: 0.14375001, y: 0.059375003}
    edgeCollider2D: []
    attachPoints: []
  - name: platform
    boundsData:
    - {x: 0.0015624911, y: 0, z: 0}
    - {x: 0.390625, y: 0.06875, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624911, y: 0, z: 0}
    - {x: 0.390625, y: 0.06875, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.19375001, y: -0.034375, z: 0}
    - {x: 0.19687499, y: -0.034375, z: 0}
    - {x: -0.19375001, y: 0.034375, z: 0}
    - {x: 0.19687499, y: 0.034375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.003908203, y: 0.81250095}
    - {x: 0.24804492, y: 0.81250095}
    - {x: 0.003908203, y: 0.8339834}
    - {x: 0.24804492, y: 0.8339834}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: c6c9f7d3fb91a8d4db53b929d3b367df
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 2
    customColliders: []
    colliderVertices:
    - {x: 0.0015624911, y: 0, z: 0}
    - {x: 0.1953125, y: 0.034375, z: 0.1}
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: platform_spiked
    boundsData:
    - {x: 0.0015624911, y: 0, z: 0}
    - {x: 0.390625, y: 0.10625, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015624911, y: 0, z: 0}
    - {x: 0.390625, y: 0.10625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.19375001, y: -0.053125, z: 0}
    - {x: 0.19687499, y: -0.053125, z: 0}
    - {x: -0.19375001, y: 0.053125, z: 0}
    - {x: 0.19687499, y: 0.053125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7226582, y: 0.9101572}
    - {x: 0.9667949, y: 0.9101572}
    - {x: 0.7226582, y: 0.9433584}
    - {x: 0.9667949, y: 0.9433584}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 129360c4101c34f4f8a5650c70863c8c
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 3
    customColliders: []
    colliderVertices:
    - {x: 0.13437499, y: 0.012500003, z: -0.1}
    - {x: 0.13437499, y: 0.012500003, z: 0.1}
    - {x: 0.121875, y: 0.03125, z: -0.1}
    - {x: 0.121875, y: 0.03125, z: 0.1}
    - {x: 0.08437501, y: 0.012500003, z: -0.1}
    - {x: 0.08437501, y: 0.012500003, z: 0.1}
    - {x: 0.03125, y: 0.012500003, z: -0.1}
    - {x: 0.03125, y: 0.012500003, z: 0.1}
    - {x: 0.009374991, y: 0.046875, z: -0.1}
    - {x: 0.009374991, y: 0.046875, z: 0.1}
    - {x: -0.015625, y: 0.012500003, z: -0.1}
    - {x: -0.015625, y: 0.012500003, z: 0.1}
    - {x: -0.081250004, y: 0.012500003, z: -0.1}
    - {x: -0.081250004, y: 0.012500003, z: 0.1}
    - {x: -0.103125006, y: 0.03125, z: -0.1}
    - {x: -0.103125006, y: 0.03125, z: 0.1}
    - {x: -0.12187501, y: 0.012500003, z: -0.1}
    - {x: -0.12187501, y: 0.012500003, z: 0.1}
    - {x: -0.18750001, y: 0.012500003, z: -0.1}
    - {x: -0.18750001, y: 0.012500003, z: 0.1}
    - {x: -0.18750001, y: -0.046875, z: -0.1}
    - {x: -0.18750001, y: -0.046875, z: 0.1}
    - {x: 0.190625, y: -0.046875, z: -0.1}
    - {x: 0.190625, y: -0.046875, z: 0.1}
    - {x: 0.190625, y: 0.012500003, z: -0.1}
    - {x: 0.190625, y: 0.012500003, z: 0.1}
    colliderIndicesFwd: 0200000001000000000000000200000003000000010000000400000005000000020000000200000005000000030000000600000007000000040000000400000007000000050000000800000007000000060000000800000009000000070000000a0000000b00000008000000080000000b000000090000000c0000000d0000000a0000000a0000000d0000000b0000000e0000000f0000000c0000000c0000000f0000000d00000010000000110000000e0000000e000000110000000f000000120000001300000010000000100000001300000011000000140000001500000012000000120000001500000013000000160000001500000014000000160000001700000015000000180000001700000016000000180000001900000017000000000000000100000018000000180000000100000019000000
    colliderIndicesBack: 000000000100000002000000010000000300000002000000020000000500000004000000030000000500000002000000040000000700000006000000050000000700000004000000060000000700000008000000070000000900000008000000080000000b0000000a000000090000000b000000080000000a0000000d0000000c0000000b0000000d0000000a0000000c0000000f0000000e0000000d0000000f0000000c0000000e00000011000000100000000f000000110000000e000000100000001300000012000000110000001300000010000000120000001500000014000000130000001500000012000000140000001500000016000000150000001700000016000000160000001700000018000000170000001900000018000000180000000100000000000000190000000100000018000000
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D:
    - points:
      - {x: 0.13437499, y: 0.012500003}
      - {x: 0.121875, y: 0.03125}
      - {x: 0.08437501, y: 0.012500003}
      - {x: 0.03125, y: 0.012500003}
      - {x: 0.009374991, y: 0.046875}
      - {x: -0.015625, y: 0.012500003}
      - {x: -0.081250004, y: 0.012500003}
      - {x: -0.103125006, y: 0.03125}
      - {x: -0.12187501, y: 0.012500003}
      - {x: -0.18750001, y: 0.012500003}
      - {x: -0.18750001, y: -0.046875}
      - {x: 0.190625, y: -0.046875}
      - {x: 0.190625, y: 0.012500003}
    edgeCollider2D: []
    attachPoints: []
  - name: 2dtoolkit_logo
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.4, y: 0.4, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.4, y: 0.4, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.2, y: -0.2, z: 0}
    - {x: 0.2, y: -0.2, z: 0}
    - {x: -0.2, y: 0.2, z: 0}
    - {x: 0.2, y: 0.2, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.7226582, y: 0.78125095}
    - {x: 0.9726543, y: 0.78125095}
    - {x: 0.7226582, y: 0.90624905}
    - {x: 0.9726543, y: 0.90624905}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: d4b13e5fdf4b0f540be8e00a364ddd35
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: background
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.025, z: 0}
    - {x: 0.025, y: -0.025, z: 0}
    - {x: -0.025, y: 0.025, z: 0}
    - {x: 0.025, y: 0.025, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.28125098}
    - {x: 0.35937303, y: 0.28125098}
    - {x: 0.32812697, y: 0.29687402}
    - {x: 0.35937303, y: 0.29687402}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: d87ec7b1d50a9164f85cac54c36371e8
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: slicedButton_down
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.025, z: 0}
    - {x: 0.025, y: -0.025, z: 0}
    - {x: -0.025, y: 0.025, z: 0}
    - {x: 0.025, y: 0.025, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.30078223}
    - {x: 0.35937303, y: 0.30078223}
    - {x: 0.32812697, y: 0.31640527}
    - {x: 0.35937303, y: 0.31640527}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 8eecf2da5d1c91845ac19745f5828760
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: slicedButton_up
    boundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.05, y: 0.05, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.025, z: 0}
    - {x: 0.025, y: -0.025, z: 0}
    - {x: -0.025, y: 0.025, z: 0}
    - {x: 0.025, y: 0.025, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.32031348}
    - {x: 0.35937303, y: 0.32031348}
    - {x: 0.32812697, y: 0.33593652}
    - {x: 0.35937303, y: 0.33593652}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: e322d00650c45ff49ad06ce7a54418a6
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: attachpoint_glasses
    boundsData:
    - {x: 0.0015625001, y: 0, z: 0}
    - {x: 0.015625, y: 0.00625, z: 0}
    untrimmedBoundsData:
    - {x: 0.0015625001, y: 0, z: 0}
    - {x: 0.015625, y: 0.00625, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.00625, y: -0.003125, z: 0}
    - {x: 0.009375, y: -0.003125, z: 0}
    - {x: -0.00625, y: 0.003125, z: 0}
    - {x: 0.009375, y: 0.003125, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.99023634, y: 0.11523535}
    - {x: 0.99023634, y: 0.12011621}
    - {x: 0.99413866, y: 0.11523535}
    - {x: 0.99413866, y: 0.12011621}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 67d25fa2d2d3a44e3a662cc8b83e1ee1
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: attachpoint_gun
    boundsData:
    - {x: 0, y: -0.0015624985, z: 0}
    - {x: 0.03125, y: 0.040625002, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: -0.0015624985, z: 0}
    - {x: 0.03125, y: 0.040625002, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.015625, y: -0.021875, z: 0}
    - {x: 0.015625, y: -0.021875, z: 0}
    - {x: -0.015625, y: 0.018750003, z: 0}
    - {x: 0.015625, y: 0.018750003, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.33984473}
    - {x: 0.32812697, y: 0.3496084}
    - {x: 0.35351366, y: 0.33984473}
    - {x: 0.35351366, y: 0.3496084}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: 2733d522def134859aee48fa56620cc2
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints: []
  - name: attachpoint_walk/0
    boundsData:
    - {x: 0.0031249998, y: -0.028125001, z: 0}
    - {x: 0.05625, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.1, z: 0}
    - {x: 0.03125, y: -0.1, z: 0}
    - {x: -0.025, y: 0.04375, z: 0}
    - {x: 0.03125, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.32910255}
    - {x: 0.8769551, y: 0.3466787}
    - {x: 0.9667949, y: 0.32910255}
    - {x: 0.9667949, y: 0.3466787}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 0
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: -0.0093749985, y: -0.0375, z: 0}
      angle: 0
    - name: glasses
      position: {x: 0.0093749985, y: 0.031250007, z: 0}
      angle: 0
  - name: attachpoint_walk/1
    boundsData:
    - {x: -0.0015625004, y: -0.028125001, z: 0}
    - {x: 0.034375, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.01875, y: -0.1, z: 0}
    - {x: 0.015625, y: -0.1, z: 0}
    - {x: -0.01875, y: 0.04375, z: 0}
    - {x: 0.015625, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.97461134, y: 0.28515723}
    - {x: 0.9960918, y: 0.28515723}
    - {x: 0.97461134, y: 0.33007714}
    - {x: 0.9960918, y: 0.33007714}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 32
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: -0.0031250007, y: -0.040625002, z: 0}
      angle: 0
    - name: glasses
      position: {x: 0.0062500015, y: 0.031250007, z: 0}
      angle: 0
  - name: attachpoint_walk/2
    boundsData:
    - {x: 0, y: -0.028125001, z: 0}
    - {x: 0.03125, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.015625, y: -0.1, z: 0}
    - {x: 0.015625, y: -0.1, z: 0}
    - {x: -0.015625, y: 0.04375, z: 0}
    - {x: 0.015625, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.32812697, y: 0.3535166}
    - {x: 0.34765428, y: 0.3535166}
    - {x: 0.32812697, y: 0.39843652}
    - {x: 0.34765428, y: 0.39843652}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 64
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: 0.012499999, y: -0.0375, z: 0}
      angle: 0
    - name: glasses
      position: {x: 0.0062500015, y: 0.031250007, z: 0}
      angle: 0
  - name: attachpoint_walk/3
    boundsData:
    - {x: 0.0031249998, y: -0.028125001, z: 0}
    - {x: 0.05625, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.025, y: -0.1, z: 0}
    - {x: 0.03125, y: -0.1, z: 0}
    - {x: -0.025, y: 0.04375, z: 0}
    - {x: 0.03125, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.8769551, y: 0.35058692}
    - {x: 0.8769551, y: 0.36816308}
    - {x: 0.9667949, y: 0.35058692}
    - {x: 0.9667949, y: 0.36816308}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 0}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 96
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 1
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: 0.031250004, y: -0.034374997, z: 0}
      angle: 33
    - name: glasses
      position: {x: 0.0093749985, y: 0.031250007, z: 0}
      angle: 0
  - name: attachpoint_walk/4
    boundsData:
    - {x: -0.0015625004, y: -0.028125001, z: 0}
    - {x: 0.034375, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.01875, y: -0.1, z: 0}
    - {x: 0.015625, y: -0.1, z: 0}
    - {x: -0.01875, y: 0.04375, z: 0}
    - {x: 0.015625, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.97461134, y: 0.33398536}
    - {x: 0.9960918, y: 0.33398536}
    - {x: 0.97461134, y: 0.37890527}
    - {x: 0.9960918, y: 0.37890527}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 128
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: 0.012499999, y: -0.0375, z: 0}
      angle: 0
    - name: glasses
      position: {x: 0.0062500015, y: 0.031250007, z: 0}
      angle: 0
  - name: attachpoint_walk/5
    boundsData:
    - {x: -0.0015624999, y: -0.028125001, z: 0}
    - {x: 0.028125, y: 0.14375, z: 0}
    untrimmedBoundsData:
    - {x: 0, y: 0, z: 0}
    - {x: 0.1, y: 0.2, z: 0}
    texelSize: {x: 0.003125, y: 0.003125}
    positions:
    - {x: -0.015625, y: -0.1, z: 0}
    - {x: 0.0125, y: -0.1, z: 0}
    - {x: -0.015625, y: 0.04375, z: 0}
    - {x: 0.0125, y: 0.04375, z: 0}
    normals: []
    tangents: []
    uvs:
    - {x: 0.97461134, y: 0.5820322}
    - {x: 0.99218553, y: 0.5820322}
    - {x: 0.97461134, y: 0.6269522}
    - {x: 0.99218553, y: 0.6269522}
    normalizedUvs:
    - {x: 0, y: 0}
    - {x: 1, y: 0}
    - {x: 0, y: 1}
    - {x: 1, y: 1}
    indices: 000000000300000001000000020000000300000000000000
    material: {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
    materialId: 0
    sourceTextureGUID: fa207f7bcc782490fa19b3fa60b34f8e
    extractRegion: 1
    regionX: 160
    regionY: 0
    regionW: 32
    regionH: 64
    flipped: 0
    complexGeometry: 0
    physicsEngine: 0
    colliderType: 0
    customColliders: []
    colliderVertices: []
    colliderIndicesFwd: 
    colliderIndicesBack: 
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    polygonCollider2D: []
    edgeCollider2D: []
    attachPoints:
    - name: gun
      position: {x: -0.0031250007, y: -0.040625002, z: 0}
      angle: 0
    - name: glasses
      position: {x: 0.0062500015, y: 0.031250007, z: 0}
      angle: 0
  premultipliedAlpha: 1
  material: {fileID: 0}
  materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  textures:
  - {fileID: 2800000, guid: 8b590bbce5acef3498615fcb7abfec2f, type: 3}
  pngTextures: []
  materialPngTextureId: 00000000
  textureFilterMode: 1
  textureMipMaps: 0
  allowMultipleAtlases: 0
  spriteCollectionGUID: 71d75aef51c47034082eca27f2eb6e30
  spriteCollectionName: DemoSpriteCollection
  assetName: 
  loadable: 0
  invOrthoSize: 1
  halfTargetHeight: 320
  buildKey: 1879071089
  dataGuid: b5aec6ebd1a8ffc458a2e46a3236acab
  managedSpriteCollection: 0
  hasPlatformData: 0
  spriteCollectionPlatforms: []
  spriteCollectionPlatformGUIDs: []
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

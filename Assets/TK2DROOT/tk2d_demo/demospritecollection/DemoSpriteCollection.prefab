%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100000
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 400000}
  - 114: {fileID: 11400000}
  m_Layer: 0
  m_Name: DemoSpriteCollection
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1002 &100001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!4 &400000
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!1002 &400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 100000}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 84c7a4b4ee15890498f818262bca0312, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  textures: []
  textureRefs: []
  spriteSheets:
  - texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    tilesX: 0
    tilesY: 0
    numTiles: 0
    anchor: 4
    pad: 0
    scale: {x: 1, y: 1, z: 1}
    additive: 0
    active: 1
    tileWidth: 32
    tileHeight: 64
    tileMarginX: 0
    tileMarginY: 0
    tileSpacingX: 0
    tileSpacingY: 0
    splitMethod: 0
    version: 1
    colliderType: 0
  fonts: []
  defaults:
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    anchor: 4
    pad: 0
    colliderType: 0
  platforms:
  - name: 
    spriteCollection: {fileID: 0}
  managedSpriteCollection: 0
  linkParent: {fileID: 0}
  loadable: 0
  atlasFormat: 0
  maxTextureSize: 1024
  forceTextureSize: 0
  forcedTextureWidth: 1024
  forcedTextureHeight: 1024
  textureCompression: 0
  atlasWidth: 512
  atlasHeight: 1024
  forceSquareAtlas: 0
  atlasWastage: 6.261635
  allowMultipleAtlases: 0
  removeDuplicates: 1
  textureParams:
  - name: bubble_green
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 7aca923e78526cd4b984265f396e04d8, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 20
    anchorY: 23
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: bubble_red
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: bb6c9fb18b1dc46499fc0567ff873e42, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 16
    anchorY: 18
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: bubble_orange
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 4a3adedb01045d0458d6e6330d1dd25d, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 16
    anchorY: 18
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: sun
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 60ab1d7b85ae054498eb9122996758d9, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 71
    anchorY: 71
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: bg
    disableTrimming: 0
    additive: 0
    scale: {x: 10, y: 10, z: 10}
    texture: {fileID: 2800000, guid: b5e1233ff1ab3d14a8b3e3910c4c8281, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 51
    anchorY: 34
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 2
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke0
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: c9b9253339e73124bbe3b5012f4bc936, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 22
    anchorY: 22
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke4
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 39734a83ab620ae42b7fd778891e490e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 40
    anchorY: 36
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 287c70551d47fed40aaed1d763b2c586, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 35
    anchorY: 36
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke3
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f54a71b7ee1fcd541859ccce61c15d4f, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 42
    anchorY: 42
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: e89d30fcbd3b3cb4f81b9f612565cd15, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 33
    anchorY: 33
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: explosion_beam
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 972d12151f1a30d4fb5c0457807734e9, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 14
    anchorY: 67
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 6109dd1f0c6a20e40b9a5cc3cd4bbfaa, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 3
    anchorY: 3
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: core
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 41539258830f7a24abc38d8bbe487af3, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 63
    anchorY: 59
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: bg1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 0d86fdb28836c6445974f98f412dfbf2, type: 3}
    materialId: 0
    anchor: 7
    anchorX: 512
    anchorY: 162
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 1
    diceUnitX: 64
    diceUnitY: 512
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: sheep_0
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: c25d0635778cf614eb2cca727e071fd6, type: 3}
    materialId: 0
    anchor: 9
    anchorX: 10
    anchorY: 16
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: sheep_1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: d20c61a95bed34948a4408be780c3d55, type: 3}
    materialId: 0
    anchor: 9
    anchorX: 9
    anchorY: 16
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: sheep_2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: df162a39ace0bbb4dbbe4c348669075c, type: 3}
    materialId: 0
    anchor: 9
    anchorX: 8
    anchorY: 16
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: cloud1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 4a902aa2ec2deaa469755f61aa3e8134, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 72
    anchorY: 35
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: cloud2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: a329c3266c887ba44b0bf7c627043688, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 91
    anchorY: 44
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: Brown Block
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 9fc5fcd3fc8803f4caf11d337579f6c8, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 50
    anchorY: 85
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: Chest Closed
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 6b3b26a1fb4ebcf448d47859ec1cb66f, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 50
    anchorY: 85
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: Stone Block
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 2deb4cd15b4f4144894c8b9f4f1a19a3, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 50
    anchorY: 85
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: Rock
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 82f0ed3ac7232ae4c8b948fb7654f205, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 50
    anchorY: 85
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: crate
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f6724743a506f464ea1684044c461aef, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 32
    anchorY: 33
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 2
    colliderData: []
    boxColliderMin: {x: 9, y: 10}
    boxColliderMax: {x: 56, y: 56}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: ground
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 2f1986f4c79909a4ca592f02dec914e4, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 181
    anchorY: 14
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 100
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: smoke5
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 3fe04aba96e31d748a88af0fdc870d13, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 1
    anchorY: 1
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: animdemo_4
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 27e59c976f45f48488bd746d93f38085, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 27
    anchorY: 27
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: animdemo_5
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: ce42d39374f446b43974cdf748be1594, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 27
    anchorY: 27
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: animdemo_1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 7a1c114efdc6f1548b40b62b7666cf2e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 27
    anchorY: 27
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: animdemo_3
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: caa05169d2cbf6b4d8d7748e71781a40, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 27
    anchorY: 27
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: animdemo_2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: e84a1e8c2679c6347b789eb93ff1893f, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 27
    anchorY: 27
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: button_down
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: f31ec590f22df524e97e5d2f9139ec46, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 141
    anchorY: 38
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: button_up
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: a6bc359a914e1df468afe824439ede88, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 141
    anchorY: 38
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: wavyground
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: abd2d4d8e662ace4c961589c5d18e01f, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 223
    anchorY: 38
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 4
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands:
    - connected: 1
      points:
      - {x: 387, y: 31}
      - {x: 332, y: 38}
      - {x: 310, y: 38}
      - {x: 277, y: 25}
      - {x: 260, y: 18}
      - {x: 244, y: 16}
      - {x: 217, y: 22}
      - {x: 191, y: 30}
      - {x: 163, y: 35}
      - {x: 142, y: 30}
      - {x: 120, y: 20}
      - {x: 99, y: 11}
      - {x: 77, y: 7}
      - {x: 36, y: 17}
      - {x: 0, y: 33}
      - {x: 0, y: 77}
      - {x: 447, y: 77}
      - {x: 447, y: 28}
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 3
    attachPoints: []
  - name: starspike
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fe18dfa9012243c4ea82497709a18b26, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 45
    anchorY: 45
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 4
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands:
    - connected: 1
      points:
      - {x: 46, y: 40}
      - {x: 40, y: 0}
      - {x: 40, y: 47}
      - {x: 1, y: 40}
      - {x: 44, y: 55}
      - {x: 27, y: 90}
      - {x: 54, y: 53}
      - {x: 83, y: 82}
      - {x: 55, y: 44}
      - {x: 91, y: 26}
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 3
    attachPoints: []
  - name: platform
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: c6c9f7d3fb91a8d4db53b929d3b367df, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 62
    anchorY: 11
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 2
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: platform_spiked
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 129360c4101c34f4f8a5650c70863c8c, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 62
    anchorY: 17
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 4
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands:
    - connected: 1
      points:
      - {x: 105, y: 13}
      - {x: 101, y: 7}
      - {x: 89, y: 13}
      - {x: 72, y: 13}
      - {x: 65, y: 2}
      - {x: 57, y: 13}
      - {x: 36, y: 13}
      - {x: 29, y: 7}
      - {x: 23, y: 13}
      - {x: 2, y: 13}
      - {x: 2, y: 32}
      - {x: 123, y: 32}
      - {x: 123, y: 13}
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 3
    attachPoints: []
  - name: 2dtoolkit_logo
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: d4b13e5fdf4b0f540be8e00a364ddd35, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 64
    anchorY: 64
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: background
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: d87ec7b1d50a9164f85cac54c36371e8, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 8
    anchorY: 8
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: slicedButton_down
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 8eecf2da5d1c91845ac19745f5828760, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 8
    anchorY: 8
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: slicedButton_up
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: e322d00650c45ff49ad06ce7a54418a6, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 8
    anchorY: 8
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 0
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 0
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: attachpoint_glasses
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 67d25fa2d2d3a44e3a662cc8b83e1ee1, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: attachpoint_gun
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: 2733d522def134859aee48fa56620cc2, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 0
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 0
    regionX: 0
    regionY: 0
    regionW: 0
    regionH: 0
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints: []
  - name: attachpoint_walk/0
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 0
    spriteSheetY: 0
    extractRegion: 1
    regionX: 0
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 0
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 13, y: 44, z: 0}
      angle: 0
    - name: glasses
      position: {x: 19, y: 22, z: 0}
      angle: 0
  - name: attachpoint_walk/1
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 1
    spriteSheetY: 0
    extractRegion: 1
    regionX: 32
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 1
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 15, y: 45, z: 0}
      angle: 0
    - name: glasses
      position: {x: 18, y: 22, z: 0}
      angle: 0
  - name: attachpoint_walk/2
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 2
    spriteSheetY: 0
    extractRegion: 1
    regionX: 64
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 2
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 20, y: 44, z: 0}
      angle: 0
    - name: glasses
      position: {x: 18, y: 22, z: 0}
      angle: 0
  - name: attachpoint_walk/3
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 3
    spriteSheetY: 0
    extractRegion: 1
    regionX: 96
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 3
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 26, y: 43, z: 0}
      angle: 33
    - name: glasses
      position: {x: 19, y: 22, z: 0}
      angle: 0
  - name: attachpoint_walk/4
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 4
    spriteSheetY: 0
    extractRegion: 1
    regionX: 128
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 4
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 20, y: 44, z: 0}
      angle: 0
    - name: glasses
      position: {x: 18, y: 22, z: 0}
      angle: 0
  - name: attachpoint_walk/5
    disableTrimming: 0
    additive: 0
    scale: {x: 1, y: 1, z: 1}
    texture: {fileID: 2800000, guid: fa207f7bcc782490fa19b3fa60b34f8e, type: 3}
    materialId: 0
    anchor: 4
    anchorX: 0
    anchorY: 0
    overrideMesh: {fileID: 0}
    doubleSidedSprite: 0
    customSpriteGeometry: 0
    geometryIslands: []
    dice: 0
    diceUnitX: 64
    diceUnitY: 64
    diceFilter: 0
    pad: 0
    extraPadding: 0
    source: 0
    fromSpriteSheet: 0
    hasSpriteSheetId: 1
    spriteSheetId: 0
    spriteSheetX: 5
    spriteSheetY: 0
    extractRegion: 1
    regionX: 160
    regionY: 0
    regionW: 32
    regionH: 64
    regionId: 5
    colliderType: 0
    colliderData: []
    boxColliderMin: {x: 0, y: 0}
    boxColliderMax: {x: 0, y: 0}
    polyColliderIslands: []
    polyColliderCap: 1
    colliderConvex: 0
    colliderSmoothSphereCollisions: 0
    colliderColor: 0
    attachPoints:
    - name: gun
      position: {x: 15, y: 45, z: 0}
      angle: 0
    - name: glasses
      position: {x: 18, y: 22, z: 0}
      angle: 0
  spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  premultipliedAlpha: 1
  altMaterials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  atlasMaterials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  atlasTextures:
  - {fileID: 2800000, guid: 8b590bbce5acef3498615fcb7abfec2f, type: 3}
  atlasTextureFiles: []
  useTk2dCamera: 0
  targetHeight: 640
  targetOrthoSize: 1
  sizeDef:
    type: 0
    orthoSize: 1
    pixelsPerMeter: 20
    width: 960
    height: 640
  globalScale: 1
  globalTextureRescale: 1
  attachPointTestSprites:
  - attachPointName: gun
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    spriteId: 42
  - attachPointName: glasses
    spriteCollection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
    spriteId: 41
  pixelPerfectPointSampled: 0
  filterMode: 1
  wrapMode: 1
  userDefinedTextureSettings: 1
  mipmapEnabled: 0
  anisoLevel: 1
  physicsEngine: 0
  physicsDepth: 0.1
  disableTrimming: 0
  disableRotation: 0
  normalGenerationMode: 0
  padAmount: -1
  autoUpdate: 1
  editorDisplayScale: 11.950669
  version: 4
  assetName: 
  linkedSpriteCollections: []
--- !u!1002 &11400001
EditorExtensionImpl:
  serializedVersion: 6
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 100000}
  m_IsPrefabParent: 1
--- !u!1002 &100100001
EditorExtensionImpl:
  serializedVersion: 6

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
Scene:
  m_ObjectHideFlags: 0
  m_PVSData: 
  m_QueryMode: 1
  m_PVSObjectsArray: []
  m_PVSPortalsArray: []
  m_OcclusionBakeSettings:
    viewCellSize: 1
    bakeMode: 2
    memoryUsage: 10485760
--- !u!104 &2
RenderSettings:
  m_Fog: 0
  m_FogColor: {r: .5, g: .5, b: .5, a: 1}
  m_FogMode: 3
  m_FogDensity: .00999999978
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientLight: {r: .200000003, g: .200000003, b: .200000003, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: .5
  m_FlareStrength: 1
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 0}
  m_ObjectHideFlags: 0
--- !u!127 &3
GameManager:
  m_ObjectHideFlags: 0
--- !u!157 &4
LightmapSettings:
  m_ObjectHideFlags: 0
  m_LightProbes: {fileID: 0}
  m_Lightmaps: []
  m_LightmapsMode: 1
  m_BakedColorSpace: 0
  m_UseDualLightmapsInForward: 0
  m_LightmapEditorSettings:
    m_Resolution: 50
    m_LastUsedResolution: 0
    m_TextureWidth: 1024
    m_TextureHeight: 1024
    m_BounceBoost: 1
    m_BounceIntensity: 1
    m_SkyLightColor: {r: .860000014, g: .930000007, b: 1, a: 1}
    m_SkyLightIntensity: 0
    m_Quality: 0
    m_Bounces: 1
    m_FinalGatherRays: 1000
    m_FinalGatherContrastThreshold: .0500000007
    m_FinalGatherGradientThreshold: 0
    m_FinalGatherInterpolationPoints: 15
    m_AOAmount: 0
    m_AOMaxDistance: .100000001
    m_AOContrast: 1
    m_LODSurfaceMappingDistance: 1
    m_Padding: 0
    m_TextureCompression: 0
    m_LockAtlas: 0
--- !u!1 &13
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 25}
  - 114: {fileID: 54}
  m_Layer: 0
  m_Name: Center
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &14
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 26}
  - 33: {fileID: 46}
  - 23: {fileID: 38}
  - 114: {fileID: 55}
  m_Layer: 0
  m_Name: Sliced Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &15
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 27}
  - 33: {fileID: 47}
  - 23: {fileID: 39}
  - 114: {fileID: 56}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &18
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 30}
  - 33: {fileID: 49}
  - 23: {fileID: 41}
  - 114: {fileID: 59}
  m_Layer: 0
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &19
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 31}
  - 33: {fileID: 50}
  - 23: {fileID: 42}
  - 114: {fileID: 60}
  m_Layer: 0
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &20
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 32}
  - 33: {fileID: 51}
  - 23: {fileID: 43}
  - 114: {fileID: 61}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &21
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 33}
  - 33: {fileID: 52}
  - 23: {fileID: 44}
  - 114: {fileID: 62}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &22
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 34}
  - 33: {fileID: 53}
  - 23: {fileID: 45}
  - 114: {fileID: 63}
  m_Layer: 0
  m_Name: Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &23
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 35}
  - 114: {fileID: 64}
  m_Layer: 0
  m_Name: Lives
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &24
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 36}
  - 20: {fileID: 37}
  - 114: {fileID: 65}
  m_Layer: 0
  m_Name: RootCamera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &25
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 512, y: 384, z: 100}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 31}
  m_Father: {fileID: 36}
--- !u!4 &26
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -165, y: -92, z: 1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &27
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 287, y: -24, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 2049822679}
--- !u!4 &30
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -11, y: -12, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &31
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 25}
--- !u!4 &32
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -126, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &33
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -77, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &34
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -31, y: -57, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 35}
--- !u!4 &35
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 1020, y: 768, z: 3}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 26}
  - {fileID: 34}
  - {fileID: 33}
  - {fileID: 32}
  - {fileID: 30}
  m_Father: {fileID: 36}
--- !u!4 &36
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -100}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 25}
  - {fileID: 35}
  - {fileID: 1826557994}
  - {fileID: 1759525226}
  m_Father: {fileID: 0}
--- !u!20 &37
Camera:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: .192156866, g: .301960796, b: .474509805, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: .300000012
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 480
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 1
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!23 &38
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &39
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &41
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &42
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 32286741c6a400a44a969d7d318b1ca5, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &43
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &44
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!23 &45
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &46
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Mesh: {fileID: 0}
--- !u!33 &47
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Mesh: {fileID: 0}
--- !u!33 &49
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Mesh: {fileID: 0}
--- !u!33 &50
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Mesh: {fileID: 0}
--- !u!33 &51
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Mesh: {fileID: 0}
--- !u!33 &52
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Mesh: {fileID: 0}
--- !u!33 &53
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Mesh: {fileID: 0}
--- !u!114 &54
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 13}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b223ab045d4e8544b4e8a23f604b903, type: 1}
  m_Name: 
  anchor: -1
  _anchorPoint: 4
  anchorToNativeBounds: 0
  offset: {x: 0, y: 0}
  tk2dCamera: {fileID: 0}
  _anchorCamera: {fileID: 37}
--- !u!114 &55
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 14}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 0, g: .657381773, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 165, y: 89}
  _anchor: 0
  _borderOnly: 0
  legacyMode: 0
  borderTop: .200000003
  borderBottom: .200000003
  borderLeft: .177777782
  borderRight: .177777782
  _createBoxCollider: 0
--- !u!114 &56
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 15}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: ITEM STORE
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 4
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 10
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: ITEM STORE
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 4
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 10
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &59
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 18}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: lives
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: lives
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!114 &60
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 19}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: b5aec6ebd1a8ffc458a2e46a3236acab, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 640, y: 640, z: 640}
  _spriteId: 4
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &61
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 20}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 0
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &62
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 21}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 2
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &63
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 22}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 1
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!114 &64
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 23}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b223ab045d4e8544b4e8a23f604b903, type: 1}
  m_Name: 
  anchor: -1
  _anchorPoint: 8
  anchorToNativeBounds: 0
  offset: {x: -4, y: 0}
  tk2dCamera: {fileID: 0}
  _anchorCamera: {fileID: 37}
--- !u!114 &65
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 24}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d60c017246963b14c9806a06670568b5, type: 1}
  m_Name: 
  version: 1
  cameraSettings:
    projection: 0
    orthographicSize: 10
    orthographicPixelsPerMeter: 1
    orthographicOrigin: 0
    orthographicType: 0
    transparencySortMode: 2
    fieldOfView: 60
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 1
      height: 1
  resolutionOverride:
  - name: Wildcard Override
    matchBy: 2
    width: -1
    height: -1
    aspectRatioNumerator: 4
    aspectRatioDenominator: 3
    scale: 1
    offsetPixels: {x: 0, y: 0}
    autoScaleMode: 3
    fitMode: 1
  inheritSettings: {fileID: 0}
  nativeResolutionWidth: 1024
  nativeResolutionHeight: 768
  _unityCamera: {fileID: 37}
  viewportClippingEnabled: 0
  viewportRegion: {x: 0, y: 0, z: 100, w: 100}
  zoomFactor: 1
  forceResolutionInEditor: 1
  forceResolution: {x: 1024, y: 768}
--- !u!196 &66
NavMeshSettings:
  m_ObjectHideFlags: 0
  m_BuildSettings:
    agentRadius: .5
    agentHeight: 2
    agentSlope: 45
    agentClimb: .400000006
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    accuratePlacement: 0
    minRegionArea: 2
    widthInaccuracy: 16.666666
    heightInaccuracy: 10
  m_NavMesh: {fileID: 0}
--- !u!1 &66152879
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 66152880}
  - 23: {fileID: 66152881}
  - 33: {fileID: 66152882}
  - 114: {fileID: 66152883}
  - 114: {fileID: 66152884}
  m_Layer: 1
  m_Name: Sliced Sprite
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &66152880
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 66152879}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 301, y: -855, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1395787942}
  m_Father: {fileID: 1759958572}
--- !u!23 &66152881
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 66152879}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &66152882
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 66152879}
  m_Mesh: {fileID: 0}
--- !u!114 &66152883
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 66152879}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 300, y: 120}
  _anchor: 4
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!114 &66152884
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 66152879}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d7eeb7d27f0df004e90cb9fda2d821bd, type: 1}
  m_Name: 
  viewCamera: {fileID: 0}
  buttonDownSprite: 
  buttonUpSprite: 
  buttonPressedSprite: 
  buttonDownSound: {fileID: 0}
  buttonUpSound: {fileID: 0}
  buttonPressedSound: {fileID: 0}
  targetObject: {fileID: 0}
  messageName: 
  targetScale: 1.10000002
  scaleTime: .0500000007
  pressedWaitTime: .300000012
--- !u!1 &67863194
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 67863195}
  - 23: {fileID: 67863196}
  - 33: {fileID: 67863197}
  - 114: {fileID: 67863198}
  m_Layer: 1
  m_Name: Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &67863195
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 67863194}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 51.9393616, y: -49.0770874, z: -1}
  m_LocalScale: {x: .666000605, y: .666000605, z: .666000605}
  m_Children: []
  m_Father: {fileID: 977200161}
--- !u!23 &67863196
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 67863194}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &67863197
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 67863194}
  m_Mesh: {fileID: 0}
--- !u!114 &67863198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 67863194}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 0
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!1 &94921275
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 94921276}
  - 23: {fileID: 94921277}
  - 33: {fileID: 94921278}
  - 114: {fileID: 94921279}
  m_Layer: 1
  m_Name: Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &94921276
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 94921275}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 51.9393616, y: -49.0770874, z: -1}
  m_LocalScale: {x: .666000605, y: .666000605, z: .666000605}
  m_Children: []
  m_Father: {fileID: 544807231}
--- !u!23 &94921277
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 94921275}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &94921278
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 94921275}
  m_Mesh: {fileID: 0}
--- !u!114 &94921279
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 94921275}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 1
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!1 &134811222
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 134811223}
  - 33: {fileID: 134811225}
  - 23: {fileID: 134811224}
  - 114: {fileID: 134811226}
  m_Layer: 1
  m_Name: Heading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &134811223
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 134811222}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1556236343}
--- !u!23 &134811224
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 134811222}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &134811225
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 134811222}
  m_Mesh: {fileID: 0}
--- !u!114 &134811226
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 134811222}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: SPEAR OF HARMING
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 16
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: SPEAR OF HARMING
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 16
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &271945025
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 271945026}
  - 33: {fileID: 271945028}
  - 23: {fileID: 271945027}
  - 114: {fileID: 271945029}
  m_Layer: 1
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &271945026
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 271945025}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -34.1500244, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 777632395}
--- !u!23 &271945027
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 271945025}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &271945028
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 271945025}
  m_Mesh: {fileID: 0}
--- !u!114 &271945029
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 271945025}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: The 3d cube of doom. It gets clipped too.
  _color: {r: 1, g: 1, b: 1, a: .325490206}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 41
  _inlineStyling: 0
  _formatting: 1
  _wordWrapWidth: 445
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: The 3d cube of doom. It gets clipped too.
    color: {r: 1, g: 1, b: 1, a: .325490206}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 41
    inlineStyling: 0
    formatting: 1
    wordWrapWidth: 445
    spacing: 0
    lineSpacing: 0
--- !u!1 &289320804
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 289320805}
  - 33: {fileID: 289320807}
  - 23: {fileID: 289320806}
  - 114: {fileID: 289320808}
  m_Layer: 1
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &289320805
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 289320804}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -34.1500244, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 320127170}
--- !u!23 &289320806
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 289320804}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &289320807
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 289320804}
  m_Mesh: {fileID: 0}
--- !u!114 &289320808
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 289320804}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: So if you didn't see me at first, I got clipped out of view.
  _color: {r: 1, g: 1, b: 1, a: .325490206}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 60
  _inlineStyling: 0
  _formatting: 1
  _wordWrapWidth: 445
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: So if you didn't see me at first, I got clipped out of view.
    color: {r: 1, g: 1, b: 1, a: .325490206}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 60
    inlineStyling: 0
    formatting: 1
    wordWrapWidth: 445
    spacing: 0
    lineSpacing: 0
--- !u!1 &290432919
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 290432920}
  - 114: {fileID: 290432921}
  m_Layer: 0
  m_Name: Script
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &290432920
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 290432919}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!114 &290432921
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 290432919}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67da3fc7993ce414fb2e5e5e7c3f3948, type: 1}
  m_Name: 
  listItems: {fileID: 1759958572}
  endOfListItems: {fileID: 1317261847}
  rotatingObjects:
  - {fileID: 1826537112}
--- !u!1 &320127169
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 320127170}
  - 33: {fileID: 320127172}
  - 23: {fileID: 320127171}
  - 114: {fileID: 320127173}
  m_Layer: 1
  m_Name: '@ListItem 4'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &320127170
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 320127169}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 20, y: -634, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1736640237}
  - {fileID: 289320805}
  - {fileID: 1272366963}
  - {fileID: 1999963341}
  m_Father: {fileID: 1759958572}
--- !u!23 &320127171
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 320127169}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &320127172
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 320127169}
  m_Mesh: {fileID: 0}
--- !u!114 &320127173
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 320127169}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .87485677, g: .186567187, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 560, y: 150}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &409047623
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 409047624}
  - 33: {fileID: 409047626}
  - 23: {fileID: 409047625}
  - 114: {fileID: 409047627}
  m_Layer: 1
  m_Name: Heading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &409047624
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 409047623}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 977200161}
--- !u!23 &409047625
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 409047623}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &409047626
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 409047623}
  m_Mesh: {fileID: 0}
--- !u!114 &409047627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 409047623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: MASTERS MACE
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 13
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: MASTERS MACE
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 13
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &474883070
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 474883071}
  - 23: {fileID: 474883072}
  - 33: {fileID: 474883073}
  - 114: {fileID: 474883074}
  m_Layer: 1
  m_Name: Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &474883071
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 474883070}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 51.9393616, y: -49.0770874, z: -1}
  m_LocalScale: {x: .666000605, y: .666000605, z: .666000605}
  m_Children: []
  m_Father: {fileID: 1556236343}
--- !u!23 &474883072
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 474883070}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &474883073
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 474883070}
  m_Mesh: {fileID: 0}
--- !u!114 &474883074
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 474883070}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 1
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!1 &544807230
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 544807231}
  - 33: {fileID: 544807233}
  - 23: {fileID: 544807232}
  - 114: {fileID: 544807234}
  m_Layer: 1
  m_Name: '@ListItem 3'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &544807231
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 544807230}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 20, y: -478, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 771955124}
  - {fileID: 1188779429}
  - {fileID: 94921276}
  - {fileID: 567404637}
  m_Father: {fileID: 1759958572}
--- !u!23 &544807232
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 544807230}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &544807233
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 544807230}
  m_Mesh: {fileID: 0}
--- !u!114 &544807234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 544807230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .87485677, g: .186567187, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 560, y: 150}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &567404636
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 567404637}
  - 33: {fileID: 567404639}
  - 23: {fileID: 567404638}
  - 114: {fileID: 567404640}
  m_Layer: 1
  m_Name: Price
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &567404637
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 567404636}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 547.146362, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 544807231}
--- !u!23 &567404638
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 567404636}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &567404639
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 567404636}
  m_Mesh: {fileID: 0}
--- !u!114 &567404640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 567404636}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: $1
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 8
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: $1
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 8
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &771955123
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 771955124}
  - 33: {fileID: 771955126}
  - 23: {fileID: 771955125}
  - 114: {fileID: 771955127}
  m_Layer: 1
  m_Name: Heading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &771955124
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 771955123}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 544807231}
--- !u!23 &771955125
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 771955123}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &771955126
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 771955123}
  m_Mesh: {fileID: 0}
--- !u!114 &771955127
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 771955123}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: STICK OF POKING
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 15
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: STICK OF POKING
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 15
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &773445814
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 773445815}
  - 33: {fileID: 773445817}
  - 23: {fileID: 773445816}
  - 114: {fileID: 773445818}
  m_Layer: 1
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &773445815
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 773445814}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -34.1500244, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 977200161}
--- !u!23 &773445816
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 773445814}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &773445817
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 773445814}
  m_Mesh: {fileID: 0}
--- !u!114 &773445818
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 773445814}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: The Masters Mace is a true masters weapon. Get one today and learn its intricracies.
  _color: {r: 1, g: 1, b: 1, a: .325490206}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 84
  _inlineStyling: 0
  _formatting: 1
  _wordWrapWidth: 445
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: The Masters Mace is a true masters weapon. Get one today and learn its intricracies.
    color: {r: 1, g: 1, b: 1, a: .325490206}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 84
    inlineStyling: 0
    formatting: 1
    wordWrapWidth: 445
    spacing: 0
    lineSpacing: 0
--- !u!1 &777632394
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 777632395}
  - 33: {fileID: 777632397}
  - 23: {fileID: 777632396}
  - 114: {fileID: 777632398}
  m_Layer: 1
  m_Name: '@ListItem 1'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &777632395
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 777632394}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 20, y: -166, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1826537112}
  - {fileID: 1943790683}
  - {fileID: 271945026}
  - {fileID: 1990124170}
  m_Father: {fileID: 1759958572}
--- !u!23 &777632396
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 777632394}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &777632397
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 777632394}
  m_Mesh: {fileID: 0}
--- !u!114 &777632398
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 777632394}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .87485677, g: .186567187, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 560, y: 150}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &977200160
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 977200161}
  - 33: {fileID: 977200163}
  - 23: {fileID: 977200162}
  - 114: {fileID: 977200164}
  m_Layer: 1
  m_Name: '@ListItem 2'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &977200161
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 977200160}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 20, y: -322, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 409047624}
  - {fileID: 773445815}
  - {fileID: 67863195}
  - {fileID: 1510099903}
  m_Father: {fileID: 1759958572}
--- !u!23 &977200162
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 977200160}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &977200163
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 977200160}
  m_Mesh: {fileID: 0}
--- !u!114 &977200164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 977200160}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .87485677, g: .186567187, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 560, y: 150}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &1022680560
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1022680561}
  - 33: {fileID: 1022680563}
  - 65: {fileID: 1022680564}
  - 23: {fileID: 1022680562}
  m_Layer: 1
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1022680561
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1022680560}
  m_LocalRotation: {x: -5.21691277e-07, y: -2.49308016e-07, z: 1.16639058e-07, w: 1}
  m_LocalPosition: {x: -4.19770458e-07, y: .632859945, z: -8.71730776e-07}
  m_LocalScale: {x: .300000012, y: .300000012, z: .300000012}
  m_Children: []
  m_Father: {fileID: 1826537112}
--- !u!23 &1022680562
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1022680560}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 10302, guid: 0000000000000000e000000000000000, type: 0}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1022680563
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1022680560}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &1022680564
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1022680560}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1188779428
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1188779429}
  - 33: {fileID: 1188779431}
  - 23: {fileID: 1188779430}
  - 114: {fileID: 1188779432}
  m_Layer: 1
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1188779429
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1188779428}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -34.1500244, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 544807231}
--- !u!23 &1188779430
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1188779428}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1188779431
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1188779428}
  m_Mesh: {fileID: 0}
--- !u!114 &1188779432
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1188779428}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: A poking stick. Use it to poke your enemies. Its pretty useless.
  _color: {r: 1, g: 1, b: 1, a: .325490206}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 64
  _inlineStyling: 0
  _formatting: 1
  _wordWrapWidth: 445
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: A poking stick. Use it to poke your enemies. Its pretty useless.
    color: {r: 1, g: 1, b: 1, a: .325490206}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 64
    inlineStyling: 0
    formatting: 1
    wordWrapWidth: 445
    spacing: 0
    lineSpacing: 0
--- !u!1 &1233523825
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1233523826}
  - 108: {fileID: 1233523827}
  m_Layer: 0
  m_Name: Directional light (for cube)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1233523826
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1233523825}
  m_LocalRotation: {x: .408217937, y: -.234569728, z: .109381661, w: .875426114}
  m_LocalPosition: {x: 370.59375, y: 174.171875, z: 73.3125}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
--- !u!108 &1233523827
Light:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1233523825}
  m_Enabled: 1
  serializedVersion: 3
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: .5
  m_Range: 10
  m_SpotAngle: 30
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_Strength: 1
    m_Bias: .0500000007
    m_Softness: 4
    m_SoftnessFade: 1
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_ActuallyLightmapped: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_Lightmapping: 1
  m_ShadowSamples: 1
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_IndirectIntensity: 1
  m_AreaSize: {x: 1, y: 1}
--- !u!1 &1272366962
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1272366963}
  - 23: {fileID: 1272366964}
  - 33: {fileID: 1272366965}
  - 114: {fileID: 1272366966}
  m_Layer: 1
  m_Name: Sprite0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1272366963
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1272366962}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 51.9393616, y: -49.0770874, z: -1}
  m_LocalScale: {x: .666000605, y: .666000605, z: .666000605}
  m_Children: []
  m_Father: {fileID: 320127170}
--- !u!23 &1272366964
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1272366962}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1272366965
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1272366962}
  m_Mesh: {fileID: 0}
--- !u!114 &1272366966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1272366962}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3fac570381f34f647b768dc5a5aa1364, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: 1, g: 1, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 2
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
--- !u!1 &1317261846
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1317261847}
  m_Layer: 0
  m_Name: EndOfList
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1317261847
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1317261846}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -577.933105, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1759525226}
--- !u!1 &1395787941
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1395787942}
  - 33: {fileID: 1395787944}
  - 23: {fileID: 1395787943}
  - 114: {fileID: 1395787945}
  m_Layer: 1
  m_Name: TextMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1395787942
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1395787941}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 66152880}
--- !u!23 &1395787943
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1395787941}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1395787944
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1395787941}
  m_Mesh: {fileID: 0}
--- !u!114 &1395787945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1395787941}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: 'clickable

    button'
  _color: {r: 0, g: 0, b: 0, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 4
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 75
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: 'clickable

      button'
    color: {r: 0, g: 0, b: 0, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 4
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 75
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1510099902
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1510099903}
  - 33: {fileID: 1510099905}
  - 23: {fileID: 1510099904}
  - 114: {fileID: 1510099906}
  m_Layer: 1
  m_Name: Price
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1510099903
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1510099902}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 547.146362, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 977200161}
--- !u!23 &1510099904
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1510099902}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1510099905
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1510099902}
  m_Mesh: {fileID: 0}
--- !u!114 &1510099906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1510099902}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: $50
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 8
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: $50
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 8
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1556236342
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1556236343}
  - 33: {fileID: 1556236345}
  - 23: {fileID: 1556236344}
  - 114: {fileID: 1556236346}
  m_Layer: 1
  m_Name: '@ListItem 0'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1556236343
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1556236342}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 20, y: -10, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 134811223}
  - {fileID: 2071610276}
  - {fileID: 474883071}
  - {fileID: 1963752048}
  m_Father: {fileID: 1759958572}
--- !u!23 &1556236344
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1556236342}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1556236345
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1556236342}
  m_Mesh: {fileID: 0}
--- !u!114 &1556236346
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1556236342}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .87485677, g: .186567187, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 560, y: 150}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &1736640236
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1736640237}
  - 33: {fileID: 1736640239}
  - 23: {fileID: 1736640238}
  - 114: {fileID: 1736640240}
  m_Layer: 1
  m_Name: Heading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1736640237
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1736640236}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 320127170}
--- !u!23 &1736640238
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1736640236}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1736640239
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1736640236}
  m_Mesh: {fileID: 0}
--- !u!114 &1736640240
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1736640236}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: I'M OUTSIDE THE VIEW
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 20
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: I'M OUTSIDE THE VIEW
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 20
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1759525225
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1759525226}
  - 33: {fileID: 1759525228}
  - 23: {fileID: 1759525227}
  - 114: {fileID: 1759525229}
  m_Layer: 0
  m_Name: Window
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1759525226
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1759525225}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 220, y: 670, z: 99}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1317261847}
  - {fileID: 1759958572}
  - {fileID: 2049822679}
  m_Father: {fileID: 36}
--- !u!23 &1759525227
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1759525225}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1759525228
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1759525225}
  m_Mesh: {fileID: 0}
--- !u!114 &1759525229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1759525225}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .280240595, g: .517554522, b: .552238822, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 600, y: 600}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &1759958571
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1759958572}
  m_Layer: 1
  m_Name: ListItems
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1759958572
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1759958571}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 1556236343}
  - {fileID: 777632395}
  - {fileID: 977200161}
  - {fileID: 544807231}
  - {fileID: 320127170}
  - {fileID: 66152880}
  m_Father: {fileID: 1759525226}
--- !u!1 &1826537111
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1826537112}
  - 33: {fileID: 1826537114}
  - 65: {fileID: 1826537115}
  - 23: {fileID: 1826537113}
  m_Layer: 1
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1826537112
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826537111}
  m_LocalRotation: {x: -.133938521, y: .348774105, z: -.0279093161, w: .92716676}
  m_LocalPosition: {x: 58, y: -49, z: -44}
  m_LocalScale: {x: 40, y: 40, z: 40}
  m_Children:
  - {fileID: 1022680561}
  m_Father: {fileID: 777632395}
--- !u!23 &1826537113
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826537111}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 10302, guid: 0000000000000000e000000000000000, type: 0}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1826537114
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826537111}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &1826537115
BoxCollider:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826537111}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1826557993
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1826557994}
  - 20: {fileID: 1826557995}
  - 114: {fileID: 1826557996}
  m_Layer: 0
  m_Name: ClipCamera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1826557994
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826557993}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 36}
--- !u!20 &1826557995
Camera:
  m_ObjectHideFlags: 3
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826557993}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 4
  m_BackGroundColor: {r: 1, g: 0, b: 0, a: .0196078438}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: .220703125
    y: .09765625
    width: .57421875
    height: .76953125
  near clip plane: .300000012
  far clip plane: 1000
  field of view: 60
  orthographic: 1
  orthographic size: 2.75039339
  m_Depth: 1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 2
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_HDR: 0
--- !u!114 &1826557996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1826557993}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d60c017246963b14c9806a06670568b5, type: 1}
  m_Name: 
  version: 1
  cameraSettings:
    projection: 0
    orthographicSize: 10
    orthographicPixelsPerMeter: 1
    orthographicOrigin: 0
    orthographicType: 0
    transparencySortMode: 2
    fieldOfView: 60
    rect:
      serializedVersion: 2
      x: .220703125
      y: .09765625
      width: .57421875
      height: .76953125
  resolutionOverride: []
  inheritSettings: {fileID: 65}
  nativeResolutionWidth: 960
  nativeResolutionHeight: 640
  _unityCamera: {fileID: 1826557995}
  viewportClippingEnabled: 1
  viewportRegion: {x: 226, y: 75, z: 588, w: 591}
  zoomFactor: 1
  forceResolutionInEditor: 1
  forceResolution: {x: 1024, y: 768}
--- !u!1 &1943790682
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1943790683}
  - 33: {fileID: 1943790685}
  - 23: {fileID: 1943790684}
  - 114: {fileID: 1943790686}
  m_Layer: 1
  m_Name: Heading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1943790683
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1943790682}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 777632395}
--- !u!23 &1943790684
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1943790682}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1943790685
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1943790682}
  m_Mesh: {fileID: 0}
--- !u!114 &1943790686
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1943790682}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: CUBE OF DOOM
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 19
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: CUBE OF DOOM
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 19
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1963752047
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1963752048}
  - 33: {fileID: 1963752050}
  - 23: {fileID: 1963752049}
  - 114: {fileID: 1963752051}
  m_Layer: 1
  m_Name: Price
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1963752048
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1963752047}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 547.146362, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1556236343}
--- !u!23 &1963752049
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1963752047}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1963752050
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1963752047}
  m_Mesh: {fileID: 0}
--- !u!114 &1963752051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1963752047}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: $30
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 8
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: $30
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 8
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1990124169
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1990124170}
  - 33: {fileID: 1990124172}
  - 23: {fileID: 1990124171}
  - 114: {fileID: 1990124173}
  m_Layer: 1
  m_Name: Price
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1990124170
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1990124169}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 547.146362, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 777632395}
--- !u!23 &1990124171
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1990124169}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1990124172
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1990124169}
  m_Mesh: {fileID: 0}
--- !u!114 &1990124173
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1990124169}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: $10
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 8
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: $10
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 8
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &1999963340
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 1999963341}
  - 33: {fileID: 1999963343}
  - 23: {fileID: 1999963342}
  - 114: {fileID: 1999963344}
  m_Layer: 1
  m_Name: Price
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1999963341
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1999963340}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 547.146362, y: -10, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 320127170}
--- !u!23 &1999963342
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1999963340}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &1999963343
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1999963340}
  m_Mesh: {fileID: 0}
--- !u!114 &1999963344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 1999963340}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: $1
  _color: {r: 1, g: 1, b: 1, a: 1}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 2
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 8
  _inlineStyling: 0
  _formatting: 0
  _wordWrapWidth: 0
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: $1
    color: {r: 1, g: 1, b: 1, a: 1}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 2
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 8
    inlineStyling: 0
    formatting: 0
    wordWrapWidth: 0
    spacing: 0
    lineSpacing: 0
--- !u!1 &2049822678
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 2049822679}
  - 33: {fileID: 2049822681}
  - 23: {fileID: 2049822680}
  - 114: {fileID: 2049822682}
  m_Layer: 0
  m_Name: WindowHeading
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2049822679
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2049822678}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 55, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 27}
  m_Father: {fileID: 1759525226}
--- !u!23 &2049822680
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2049822678}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 45d35519481f70649a530df6dcddd423, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &2049822681
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2049822678}
  m_Mesh: {fileID: 0}
--- !u!114 &2049822682
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2049822678}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 14088a148d1a83e4780e3db13db5b6ca, type: 1}
  m_Name: 
  collection: {fileID: 11400000, guid: 4864518b0693631458553f73aaa86021, type: 2}
  _color: {r: .186567187, g: .897609711, b: 1, a: 1}
  _scale: {x: 1, y: 1, z: 1}
  _spriteId: 3
  boxCollider: {fileID: 0}
  meshCollider: {fileID: 0}
  meshColliderPositions: []
  meshColliderMesh: {fileID: 0}
  renderLayer: 0
  _dimensions: {x: 600, y: 50}
  _anchor: 6
  _borderOnly: 0
  legacyMode: 0
  borderTop: .400000006
  borderBottom: .400000006
  borderLeft: .355555564
  borderRight: .355555564
  _createBoxCollider: 0
--- !u!1 &2071610275
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  serializedVersion: 3
  m_Component:
  - 4: {fileID: 2071610276}
  - 33: {fileID: 2071610278}
  - 23: {fileID: 2071610277}
  - 114: {fileID: 2071610279}
  m_Layer: 1
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2071610276
Transform:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2071610275}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 106, y: -34.1500244, z: -1}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 1556236343}
--- !u!23 &2071610277
Renderer:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2071610275}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_LightmapIndex: 255
  m_LightmapTilingOffset: {x: 1, y: 1, z: 0, w: 0}
  m_Materials:
  - {fileID: 2100000, guid: 11c6642b41a99794e92386ad3c244c98, type: 2}
  m_SubsetIndices: 
  m_StaticBatchRoot: {fileID: 0}
  m_UseLightProbes: 0
  m_LightProbeAnchor: {fileID: 0}
  m_ScaleInLightmap: 1
--- !u!33 &2071610278
MeshFilter:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2071610275}
  m_Mesh: {fileID: 0}
--- !u!114 &2071610279
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_GameObject: {fileID: 2071610275}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 994457055a9434b4aa7b65ad37262d67, type: 1}
  m_Name: 
  _font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
  _text: The spear of harming produces a harmful ray that kills enemies dead. Your
    enemies won't even know what hit em!
  _color: {r: 1, g: 1, b: 1, a: .325490206}
  _color2: {r: 1, g: 1, b: 1, a: 1}
  _useGradient: 0
  _textureGradient: 0
  _anchor: 0
  _scale: {x: 1, y: 1, z: 1}
  _kerning: 0
  _maxChars: 110
  _inlineStyling: 0
  _formatting: 1
  _wordWrapWidth: 445
  spacing: 0
  lineSpacing: 0
  data:
    version: 1
    font: {fileID: 11400000, guid: 3082bbcb45b417041963b6b8562266c1, type: 2}
    text: The spear of harming produces a harmful ray that kills enemies dead. Your
      enemies won't even know what hit em!
    color: {r: 1, g: 1, b: 1, a: .325490206}
    color2: {r: 1, g: 1, b: 1, a: 1}
    useGradient: 0
    textureGradient: 0
    anchor: 0
    renderLayer: 0
    scale: {x: 1, y: 1, z: 1}
    kerning: 0
    maxChars: 110
    inlineStyling: 0
    formatting: 1
    wordWrapWidth: 445
    spacing: 0
    lineSpacing: 0

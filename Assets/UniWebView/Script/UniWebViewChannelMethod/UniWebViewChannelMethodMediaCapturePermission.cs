//
//  UniWebViewChannelMethodMediaCapturePermission.cs
//  Created by <PERSON>(@onevcat) on 2024-02-20.
//
//  This file is a part of UniWebView Project (https://uniwebview.com)
//  By purchasing the asset, you are allowed to use this code in as many as projects 
//  you want, only if you publish the final products under the name of the same account
//  used for the purchase. 
//
//  This asset and all corresponding files (such as source code) are provided on an 
//  “as is” basis, without warranty of any kind, express of implied, including but not
//  limited to the warranties of merchantability, fitness for a particular purpose, and 
//  noninfringement. In no event shall the authors or copyright holders be liable for any 
//  claim, damages or other liability, whether in action of contract, tort or otherwise, 
//  arising from, out of or in connection with the software or the use of other dealing in the software.
//

using System;
using UnityEngine;

/// <summary>
/// Represents the request of media capture permission.
///
/// This class represents the request of media capture permission. When the web page requests the permission to access
/// the device's hardware, such as the camera or microphone, UniWebView will ask you to handle the request with a value
/// of this class. You can use the values in this class to determine the decision for the permission request.
/// </summary>
[Serializable]
public class UniWebViewChannelMethodMediaCapturePermission {
    
    [SerializeField]
    private string protocol;

    [SerializeField] 
    private string host;
    
    [SerializeField]
    private int port;
    
    [SerializeField] 
    private string[] resources;
    
    /// <summary>
    /// The protocol used by the permission request. Such as "https" or "http".
    /// </summary>
    public string Protocol => protocol;
    
    /// <summary>
    /// The host of the origin of the permission request.
    ///
    /// It is usually the domain of the web page.
    /// </summary>
    public string Host => host;
    
    /// <summary>
    /// The port of the origin of the permission request.
    ///
    /// If not existing in the request URL, it is -1.
    /// </summary>
    public int Port => port;
    
    /// <summary>
    /// The string representation of the resources of the origin of the permission request.
    ///
    /// An array contains requested resources, the most common values are "VIDEO", "AUDIO". On Android, some pages can
    /// also requests for "PROTECTED_MEDIA_ID", "MIDI_SYSEX", etc. 
    /// </summary>
    public string[] Resources => resources;
}
# UniWebView

Thank you for purchasing UniWebView.

UniWebView is a Unity3D plugin built on native iOS/Android technology. It helps your users to enjoy web content and 
interact with your game through the web views.

## Documentation

To get started, please visit our documentation site: [https://docs.uniwebview.com](https://docs.uniwebview.com). You
could find step-by-step guides on how to import UniWebView to your project, as well as some basic usage of this asset. 
You could also find a full script reference on the same site in this page: [https://docs.uniwebview.com/api/](https://docs.uniwebview.com/api/).

## Upgrading

All purchased customers could get all updates for the same major version for free. Please check the place you have 
purchased this asset to see whether there is an update or not. A release note is also contained in this asset, in the
"CHANGELOG.md" file. You could also find the same version list in [this page](https://docs.uniwebview.com/release-note).

## Getting Support

For frequently asked questions, we have a page to answer them. If you encountered any problems while using UniWebView, 
we strongly suggest to visit the [FAQ page](https://docs.uniwebview.com/guide/faq.html) first. Also feel free to
[submit a ticket](https://onevcat.atlassian.net/servicedesk/customer/portal/2) if you cannot find a solution, we will 
do our best to get you out!

## Other

For more information, please visit the [official web site](https://uniwebview.com) of UniWebView, or contact us through 
a ticket.

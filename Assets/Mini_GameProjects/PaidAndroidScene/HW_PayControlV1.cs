using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using CodeStage.AntiCheat.ObscuredTypes;
using DG.Tweening;
using UnityEngine.UI;
using System;

public class HW_PayControlV1 : MonoBehaviour
{

    public tk2dTextMesh priceText_base, priceText_challenge, priceText_concentration;
    public string backSceneName = "Home";
    public GameObject paidBtn, restoreBtn;

    public string productId_base, productId_challenge, productId_concentration;




    private string productId;
    int showCount = 0;

    private enum UserAction
    {
        normal,
        buy,
        restore
    }
    private UserAction userAction = UserAction.normal;

    private IAppsTeam_Huawei huawei;
    // Use this for initialization
    void Awake()
    {
        if (GlobalVariable_Android.appChannel == GlobalVariable_Android.AppChannel.Huawei)
        {
            if (huawei == null)
            {
                huawei = new GameObject("Huawei_JavaObject").AddComponent<IAppsTeam_Huawei>();
            }
        }
    }

    void Start()
    {
        GlobalVariable.GetDeviceOSLanguage();
        userAction = UserAction.normal;
        //productId = GlobalVariable_Android.FullVersion_Flag;

        productId_base = FD_GlobalVariable.productId_base;
        productId_challenge = FD_GlobalVariable.productId_challenge;
        productId_concentration = FD_GlobalVariable.productId_concentration;
        
        Init();
    }

    void Init()
    {
        if (GlobalVariable_Android.appChannel == GlobalVariable_Android.AppChannel.Huawei)
        {
            huawei.SetCallbackObjectName(gameObject.name);

            //BtnPaidScale(paidBtn);

           huawei.GetProudctDetail(productId_base);
           huawei.GetProudctDetail(productId_challenge);
           huawei.GetProudctDetail(productId_concentration);
        }
    }
    
    void PaidButton_Click_Base()
    {
        //HOTween.Pause("BtnPaidScale");
        DOTween.Pause("BtnPaidScale");
        paidBtn.transform.localScale = Vector3.one;

        productId = productId_base;

        //BuyProductCheck();
        GoToBuyProduct();
    }

    void PaidButton_Click_Challenge()
    {
        //HOTween.Pause("BtnPaidScale");
        DOTween.Pause("BtnPaidScale");
        paidBtn.transform.localScale = Vector3.one;

        productId = productId_challenge;

        //BuyProductCheck();
        GoToBuyProduct();
    }

    void PaidButton_Click_Concentration()
    {
        //HOTween.Pause("BtnPaidScale");
        DOTween.Pause("BtnPaidScale");
        paidBtn.transform.localScale = Vector3.one;

        productId = productId_concentration;
        //BuyProductCheck();
        GoToBuyProduct();
    }

    void BuyProductCheck()
    {
        string msgContent = GameUtility.LocalizedString("Loading");
        NativeDialogs.Instance.ShowProgressDialog("", msgContent, false, false);

        userAction = UserAction.buy;
        showCount = 0;

        if (GlobalVariable_Android.appChannel == GlobalVariable_Android.AppChannel.Huawei)
        {
            huawei.CheckHasPurchased(productId);
        }
    }


    void GoToBuyProduct()
    {
        showCount = 0;
        Debug.Log("GoToBuyProduct");

        if (GlobalVariable_Android.appChannel == GlobalVariable_Android.AppChannel.Huawei)
        {
            huawei.PayProduct(productId, (string result) => {

                GetPayProductResult(result);

            });
        }
    }



    void PaidButton_Click()
    {
        //HOTween.Pause("BtnPaidScale");
        DOTween.Pause("BtnPaidScale");
        paidBtn.transform.localScale = Vector3.one;

        //BuyProductCheck();
        GoToBuyProduct();
    }

    void AdsClose()
    {

        if (GlobalVariable.paidAds_FromPage == GlobalVariable.PaidAds_FromPage.Home)
        {
            GameUtility.GoToSceneName("Home");
        }
        else
        {
            GameUtility.GoToSceneName(backSceneName);
        }

    }

    public void HideProgressDialog(string result)
    {
        NativeDialogs.Instance.HideProgressDialog();

        userAction = UserAction.normal;
    }

    public void CheckHasPurchasedInfo(string result)
    {
        Debug.Log("CheckHasPurchasedInfo: " + result);

        if (result == "GoToPayProdcut")
        {
            GoToBuyProduct();
        }
    }

    public void SetPrice(string result)
    {
        string[] data_array = result.Split(':');
        string search_productId = data_array[0];
        string price = data_array[1].Replace("CNY", "¥");

        if (search_productId == productId_base)
        {
            priceText_base.text = price;
        }
        else if (search_productId == productId_challenge)
        {
            priceText_challenge.text = price;
        }
        else if (search_productId == productId_concentration)
        {
            priceText_concentration.text = price;
        }
    }

    public void GetProudctDetail_Error(string result)
    {
        Debug.Log("GetProudctDetail_Error: " + result);

        string msgContent = result;
        string strA = GameUtility.LocalizedString("OK");
        string msgTitle = GameUtility.LocalizedString("Info");

        if (result == "60050" || result == "Please sign in to the app with a HUAWEI ID")
        {
            msgContent = GameUtility.LocalizedString("PleaseLoginHuawei");
        }


        ShowMessageBox(msgTitle, msgContent, strA);
    }

    //付费购买结果
    public void GetPayProductResult(string result)
    {
        string strA = GameUtility.LocalizedString("OK");
        string msgTitle = GameUtility.LocalizedString("Info");
        string msgContent;


        //PayStatusCodes
        if (result == "ORDER_STATE_SUCCESS:" + productId || result == "ORDER_PRODUCT_OWNED")
        {

            Debug.Log("PaidAds_Control Pay Success: " + result);

            FD_GlobalVariable.current_purchase_product_id = productId;

            Application.LoadLevel("A_PaidAds_OK");

            //GameUtility.GoToSceneName_NotAddSuffix("SplashScreen");
            //Application.LoadLevel("SplashScreen");

            //ObscuredPrefs.SetBool(GlobalVariable_Android.PrefsBool, true);

            //ObscuredPrefs.Save();

            //Debug.Log("解锁成功: " + ObscuredPrefs.GetBool(GlobalVariable_Android.PrefsBool, false) + "\n");


            /*
            NativeDialogs.Instance.ShowMessageBox("", msgContent, new string[] { strA }, false,
                                                  (string b) => {

                                                      AdsClose();

                                                  });
*/
            //ShowMessageBox("", msgContent, strA);

            //AdsClose();
        }
        else if (result == "ORDER_HWID_NOT_LOGIN" || result == "2902")
        {
            Debug.Log("Pay Failed: " + result);

            msgContent = GameUtility.LocalizedString("PleaseLoginHuawei");
            ShowMessageBox(msgTitle, msgContent, strA);

            //HideProgressDialog(result);
        }
        else if (result == "30005")
        {
            Debug.Log("Pay Failed: " + result);
            msgContent = "网络连接异常";
            ShowMessageBox(msgTitle, msgContent, strA);

            //HideProgressDialog(result);
        }
        else
        {
            Debug.Log("Pay Failed: " + result);

            //HideProgressDialog(result);
        }
        userAction = UserAction.normal;
    }

    void RestoreButton_Click()
    {
        string msgContent = GameUtility.LocalizedString("Loading");
        NativeDialogs.Instance.ShowProgressDialog("", msgContent, false, false);

        userAction = UserAction.restore;
        showCount = 0;

        huawei.GetPurchaseInfo(productId);
    }



    public void NoPurchaseRecord(string result)
    {
        showCount++;
        if (showCount == 1)
        {
            Debug.Log("NoPurchaseRecord: " + result);

            string msgContent = GameUtility.LocalizedString("NoPurchaseRecord");

            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");

            ShowMessageBox(msgTitle, msgContent, strA);
        }
        userAction = UserAction.normal;
    }

    public void ShowErrorResult(string result)
    {
        Debug.Log("PaidControl ShowErrorResult: " + result);

        string msgContent = result;

        string strA = GameUtility.LocalizedString("OK");
        string msgTitle = GameUtility.LocalizedString("Info");

        if (result == "60050") msgContent = GameUtility.LocalizedString("PleaseLoginHuawei");

        ShowMessageBox(msgTitle, msgContent, strA);

        userAction = UserAction.normal;
    }

    public void ShowErrorResultForCheckPurchased(string result)
    {
        Debug.Log("ShowErrorResultForCheckPurchased: " + result);


        if (result == "60050")
        {
            string msgContent = result;
            string strA = GameUtility.LocalizedString("OK");
            string msgTitle = GameUtility.LocalizedString("Info");

            msgContent = GameUtility.LocalizedString("PleaseLoginHuawei");
            ShowMessageBox(msgTitle, msgContent, strA);
        }
        else
        {
            GoToBuyProduct();
        }

    }

    void ShowMessageBox(string title, string msgContent, string strBtn)
    {
        NativeDialogs.Instance.ShowMessageBox(title, msgContent, new string[] { strBtn }, false,
                                              (string b) => {

                                              });
    }

    void BtnPaidScale(GameObject btn)
    {
        /*
        TweenParms parms1 = new TweenParms();
        parms1.Id("BtnPaidScale");
        parms1.Prop("localScale", new Vector3(1.1f, 1.1f, 1.1f));
        parms1.AutoKill(true);
        parms1.Ease(EaseType.Linear);
        parms1.Loops(-1, LoopType.Yoyo);
        parms1.Delay(0); //延迟时间
        HOTween.To(btn.transform, 0.5f, parms1);
*/
        btn.transform.DOScale(1.1f, 0.5f).SetId("BtnPaidScale")
           .SetLoops(-1, LoopType.Yoyo).SetEase(Ease.Linear);
    }
}



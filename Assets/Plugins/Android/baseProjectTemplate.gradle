buildscript {
    repositories {

        google()
        mavenCentral()
        maven { url 'https://developer.huawei.com/repo/' }

    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.0"
        classpath 'com.huawei.agconnect:agcp:1.4.1.300'

    }
}


plugins {
    // If you are changing the Android Gradle Plugin version, make sure it is compatible with the Gradle version preinstalled with Unity
    // See which Gradle version is preinstalled with Unity here https://docs.unity3d.com/Manual/android-gradle-overview.html
    // See official Gradle and Android Gradle Plugin compatibility table here https://developer.android.com/studio/releases/gradle-plugin#updating-gradle
    // To specify a custom Gradle version in Unity, go do "Preferences > External Tools", uncheck "Gradle Installed with Unity (recommended)" and specify a path to a custom Gradle version
    id 'com.android.application' version '8.2.2' apply false
    id 'com.android.library' version '8.2.2' apply false
    **BUILD_SCRIPT_DEPS**
}

allprojects {
    repositories {

        maven {url 'https://developer.huawei.com/repo/'}
        google()
        jcenter()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" 
    xmlns:tools="http://schemas.android.com/tools" 
    android:installLocation="preferExternal">
    
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    
    <uses-feature android:glEsVersion="0x00020000" />
    <uses-feature android:name="android.hardware.touchscreen" android:required="false" />
    <uses-feature android:name="android.hardware.touchscreen.multitouch" android:required="false" />
    <uses-feature android:name="android.hardware.touchscreen.multitouch.distinct" android:required="false" />
    <uses-feature android:name="android.software.leanback" android:required="false" />
    
    <application 
        android:theme="@android:style/Theme.NoTitleBar.Fullscreen" 
        android:usesCleartextTraffic="true" 
        android:icon="@mipmap/app_icon" 
        android:label="@string/app_name" 
        android:isGame="true" 
        android:enableOnBackInvokedCallback="false" 
        android:extractNativeLibs="true">
        
        <meta-data android:name="unity.build-id" android:value="bf59f30c-df0a-4033-93e8-6d1bbfe4708f" />
        <meta-data android:name="unity.splash-mode" android:value="2" />
        <meta-data android:name="unity.splash-enable" android:value="True" />
        <meta-data android:name="android.max_aspect" android:value="2.1" />
        <meta-data android:name="com.huawei.hms.client.appid" android:value="107217069" />
        <meta-data android:name="com.huawei.hms.client.cpid" android:value="890086000102016928" />
        <meta-data android:name="unityplayer.SkipPermissionsDialog" android:value="true" />
        <meta-data android:name="unity.launch-fullscreen" android:value="True" />
        <meta-data android:name="unity.render-outside-safearea" android:value="True" />
        <meta-data android:name="notch.config" android:value="portrait|landscape" />
        <meta-data android:name="unity.auto-report-fully-drawn" android:value="true" />
        
        <activity 
            android:name="com.unity3d.player.UnityPlayerActivity" 
            android:label="@string/app_name" 
            android:screenOrientation="userLandscape" 
            android:launchMode="singleTask" 
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale|layoutDirection|density" 
            android:hardwareAccelerated="true" 
            android:resizeableActivity="false" 
            android:exported="true">
            <intent-filter>
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <meta-data android:name="unityplayer.UnityActivity" android:value="true" />
            <meta-data android:name="unityplayer.ForwardNativeEventsToDalvik" android:value="false" />
            <meta-data android:name="notch_support" android:value="true" />
        </activity>
        
        <activity android:name="com.iappsteam.Unitl.AdsUnitl" />
        <activity android:name="com.iappsteam.IAppsTeam_Huawei" />
        <activity android:name="com.iappsteam.HW_PayProduct_ResultCallback" />
    </application>
    
    <supports-screens 
        android:smallScreens="true" 
        android:normalScreens="true" 
        android:largeScreens="true" 
        android:xlargeScreens="true" 
        android:anyDensity="true" />
</manifest>
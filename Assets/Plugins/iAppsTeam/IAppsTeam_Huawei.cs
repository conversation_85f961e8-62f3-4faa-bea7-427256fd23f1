using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System;


#if UNITY_ANDROID
public class IAppsTeam_Huawei : MonoBehaviour
{
    //接口说明
    //https://developer.huawei.com/consumer/cn/service/hms/catalog/HwJointOperationApp.html?page=hmssdk_jointOper_app_devguide

    //private static AndroidJavaClass adJavaClass;
    //private static string className = "com.iappsteam.IAppsTeam_Huawei";
    private string objectName = "com.iappsteam.IAppsTeam_Huawei";

    private AndroidJavaObject adJavaObject;


    const string pluginName = "com.iappsteam.IAppsTeam_Huawei";

    class HW_PayCallback : AndroidJavaProxy
    {
        private System.Action<string> handler;
        public HW_PayCallback(System.Action<string> handlerIn) : base(pluginName + "$HW_PayCallback")
        {
            handler = handlerIn;
        }
        public void onPayComplete(string result)
        {
            //Debug.Log("PayComplete:" + result);

            if (handler != null)
                handler(result);
        }
    }



    //signIn   登录华为帐号 
    /*
    public static void SignIn()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaClass == null)
                adJavaClass = new AndroidJavaClass(className);

            Debug.Log("登录华为帐号");

            adJavaClass.CallStatic("signIn");
        }

    }
    */

    //signOut    登录华为帐号  
    /*
    public static void SignOut()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaClass == null)
                adJavaClass = new AndroidJavaClass(className);

            Debug.Log("注销华为帐号授权");

            adJavaClass.CallStatic("signOut");
        }

    }
   */
    //

    //pay      应用内支付（非托管商品方式）
    //totalAmout 支付金额(amount)
    //商品名称 productName
    //商品描述 productDesc
    //商户ID，来源于开发者联盟的“支付ID” merchantId
    // 应用ID，来源于开发者联盟 applicationID


    //商户保留信息，选填不参与签名，,若该字段有值，在华为支付服务器回调接口中原样返回。
    // 国家码 country
    //币种 currency
    // 商户名称，必填，不参与签名。开发者注册的公司名称 merchantName
    public void Pay(float totalAmout, string productName, string productDesc, string merchantId,
                           string applicationID, string extReserved = "这是测试支付的功能",
                           string country = "CN", string currency = "CNY", string merchantName = "xxxx")
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            Debug.Log("Pay");

            string[] paramas = new string[9];
            paramas[0] = totalAmout.ToString("#0.00");
            paramas[1] = productName;
            paramas[2] = productDesc;
            paramas[3] = merchantId;
            paramas[4] = applicationID;
            paramas[5] = extReserved;
            paramas[6] = country;
            paramas[7] = currency;
            paramas[8] = merchantName;
            //adJavaClass.CallStatic("pay", paramas); 
            adJavaObject.Call("pay", paramas);
        }

    }

    /*
    String amount = String.format("%.2f", totalAmount);

    //商品名称
    payReq.productName = "test product";
    //商品描述
    payReq.productDesc = "test product description";
    // 商户ID，来源于开发者联盟的“支付ID”
    payReq.merchantId = cpId;
    // 应用ID，来源于开发者联盟
    payReq.applicationID = appId;
    // 支付金额
    payReq.amount = amount;
    // 商户订单号：开发者在支付前生成，用来唯一标识一次支付请求
    payReq.requestId = requestId;
    // 国家码
    payReq.country = "CN";
    //币种
    payReq.currency = "CNY";
    // 渠道号
    payReq.sdkChannel = 1;
    // 回调接口版本号
    payReq.urlVer = "2";

    // 商户名称，必填，不参与签名。开发者注册的公司名称
    payReq.merchantName = "XXXXX";
    //分类，必填，不参与签名。该字段会影响风控策略
    // X4：主题,X5：应用商店,  X6：游戏,X7：天际通,X8：云空间,X9：电子书,X10：华为学习,X11：音乐,X12 视频,
    // X31 话费充值,X32 机票/酒店,X33 电影票,X34 团购,X35 手机预购,X36 公共缴费,X39 流量充值
    payReq.serviceCatalog = "X6"; // 应用设置为"X5"，游戏设置为"X6"
    //商户保留信息，选填不参与签名，支付成功后会华为支付平台会原样 回调CP服务端
    payReq.extReserved = "这是测试支付的功能";
    */

    //查询订单状态。getOrderDetail
    //requestId   商户订单号。开发者应用在发起支付请求前生成，用来唯一标识一次请求。
    //             该字段由字母和数字组成，必须在商户内唯一，用于唯一标识一个商户订单。
    //applicationID 应用ID。在华为开发者联盟上获取的AppID。
    //merchantId  商户id，来源：华为开发者联盟“移动应用详情页”的“支付ID”
    //productNos    商品No。
    //              需要获取价格信息的商品No列表，多个No以竖线分割，一次查询最多支持20个No。
    //              注意：所查询的productNo必须属于对应的packageName,且在应用内唯一。 
    /*
    public void GetOrderDetail(string requestId, string applicationID, string merchantId, 
                                      string productNos)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[4];
            paramas[0] = requestId;
            paramas[1] = applicationID;
            paramas[2] = merchantId;
            paramas[3] = productNos;
            adJavaObject.Call("getOrderDetail", paramas);
        }

    }
    */

    //检查更新
    public void CheckUpate()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            adJavaObject.Call("CheckUpdate");
        }
    }


    //获取商品信息
    // productId 所购买商品的PMS编码。
    public void GetProudctDetail(string productId)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[1];
            paramas[0] = productId;
            adJavaObject.Call("GetProudctDetail", paramas);
        }
    }

    //获取商品信息
    // productId 所购买商品的PMS编码。
    public void PayProduct(string productId)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[1];
            paramas[0] = productId;
            adJavaObject.Call("PayProduct", paramas);
        }
    }

    //查询曾经购买商品信息
    // productId 所购买商品的PMS编码。
    public void GetPurchaseInfo(string productId)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[1];
            paramas[0] = productId;
            adJavaObject.Call("GetPurchaseInfo", paramas);
        }
    }



    //设置返回对象名称
    public void SetCallbackObjectName(string callbackObject)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[1];
            paramas[0] = callbackObject;
            adJavaObject.Call("SetCallbackObjectName", paramas);
        }
    }


    //购买商品信息
    // productId 所购买商品的PMS编码。
    public void PayProduct(string productId, System.Action<string> payComplete)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);


            adJavaObject.Call("PayProduct", new object[] { productId, new HW_PayCallback(payComplete) });
        }
    }

    //查询曾经购买商品信息用于购买商品时使用PayProduct
    // productId 所购买商品的PMS编码。
    public void CheckHasPurchased(string productId)
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            if (adJavaObject == null)
                adJavaObject = new AndroidJavaObject(objectName);

            string[] paramas = new string[1];
            paramas[0] = productId;
            adJavaObject.Call("CheckHasPurchased", paramas);
        }
    }
}
#endif

Unity Editor version:    2022.3.61f1 (6c53ebaf375d)
Branch:                  2022.3/release
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.6.1 (Build 24G90)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        32768 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
-logFile
Logs/AssetImportWorker0.log
-srvPort
53818
Successfully changed project path to: /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8535531712]  Target information:

Player connection [8535531712]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1489620251 [EditorId] 1489620251 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1489620251 [EditorId] 1489620251 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 1489620251 [EditorId] 1489620251 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712] Host joined multi-casting on [***********:54997]...
Player connection [8535531712] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 20.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M2 Max (high power)
Metal devices available: 1
0: Apple M2 Max (high power)
Using device Apple M2 Max (high power)
Initializing Metal device caps: Apple M2 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56411
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004388 seconds.
- Loaded All Assemblies, in  0.212 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
[usbmuxd] Attached: 3 00008101-00191DE22011401E
[usbmuxd] Attached: 2 e10b21bce1b5ea565990bc8410260e0a61fa2af4
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 71 ms
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.271 seconds
Domain Reload Profiling: 483ms
	BeginReloadAssembly (62ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (93ms)
		LoadAssemblies (63ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (90ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (82ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (271ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (244ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (159ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (59ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.386 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.401 seconds
Domain Reload Profiling: 787ms
	BeginReloadAssembly (68ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (277ms)
		LoadAssemblies (214ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (92ms)
			TypeCache.Refresh (76ms)
				TypeCache.ScanAssembly (64ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (401ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (161ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3581 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (211.3 KB). Loaded Objects now: 4060.
Memory consumption went from 160.3 MB to 160.1 MB.
Total: 4.886209 ms (FindLiveObjects: 0.086042 ms CreateObjectMapping: 0.052083 ms MarkObjects: 4.620583 ms  DeleteObjects: 0.127334 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 7367.137271 seconds.
  path: Assets/GameApp/Textures/Level_Select/ln_ca_white-tc.png
  artifactKey: Guid(902b15e5fcec74dd0be896a6ffb7615b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/ln_ca_white-tc.png using Guid(902b15e5fcec74dd0be896a6ffb7615b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '85d9c3e9150126ea1230b083ae8c0277') in 0.048460 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 12.546965 seconds.
  path: Assets/GameApp/Textures/Level_Select/选择主题简.png
  artifactKey: Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/选择主题简.png using Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd3d642356c03acd44459dd7345ac9de9') in 0.005321 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.830601 seconds.
  path: Assets/GameApp/Textures/Level_Select/选择主题繁.png
  artifactKey: Guid(a2a299a3c48f14e328276719d24ec16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/选择主题繁.png using Guid(a2a299a3c48f14e328276719d24ec16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '714e90a5f22b07b1cf844d04d9040cf8') in 0.006554 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 6.179649 seconds.
  path: Assets/GameApp/Textures/Level_Select/title1-tc.png
  artifactKey: Guid(140aae2edec5242d180189d24e75c5b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/title1-tc.png using Guid(140aae2edec5242d180189d24e75c5b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3a2946fb479744ffdab12c4edcc7d202') in 0.006522 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 21.471307 seconds.
  path: Assets/GameApp/Textures/Level_Select/title-.png
  artifactKey: Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/title-.png using Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6af797aa735427dc0bde11debf17e43b') in 0.004596 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 24.235580 seconds.
  path: Assets/GameApp/Textures/Level_Select/Themes-sc.png
  artifactKey: Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/Themes-sc.png using Guid(7b6254274308e4eb0859dd69d01edf22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c841c46ace554de3e2f798c86cb9951e') in 0.005278 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 3.554143 seconds.
  path: Assets/GameApp/Textures/Level_Select/Themes-tc.png
  artifactKey: Guid(a2a299a3c48f14e328276719d24ec16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select/Themes-tc.png using Guid(a2a299a3c48f14e328276719d24ec16d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'df9e8e2ec48973806149e005abc56e71') in 0.004999 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321187000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.311 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.447 seconds
Domain Reload Profiling: 759ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (121ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (447ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (277ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (146ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.30 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3516 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.4 KB). Loaded Objects now: 4072.
Memory consumption went from 153.9 MB to 153.7 MB.
Total: 4.723542 ms (FindLiveObjects: 0.091667 ms CreateObjectMapping: 0.051292 ms MarkObjects: 4.478708 ms  DeleteObjects: 0.101500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 89.856899 seconds.
  path: Assets/LearningSuite/Scripts
  artifactKey: Guid(4f79dd9596c5148908ac8a1820a547a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/Scripts using Guid(4f79dd9596c5148908ac8a1820a547a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f9920acf6188c69a17d211de511d0609') in 0.001622 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.523 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 1234ms
	BeginReloadAssembly (324ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (159ms)
		LoadAssemblies (175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (710ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (167ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3516 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.4 KB). Loaded Objects now: 4075.
Memory consumption went from 160.5 MB to 160.3 MB.
Total: 5.237792 ms (FindLiveObjects: 0.129792 ms CreateObjectMapping: 0.057042 ms MarkObjects: 4.923291 ms  DeleteObjects: 0.127209 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 154.377825 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI/FD1_HomePage_atlas0.png
  artifactKey: Guid(95a73e1d43a4b42db88179cd5b6235d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI/FD1_HomePage_atlas0.png using Guid(95a73e1d43a4b42db88179cd5b6235d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f2e877ad44a456f66cb5816cbf6d0c38') in 0.035801 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 2.701839 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI/UserCenter_atlas0.png
  artifactKey: Guid(d83eef705968b492982a00b3baab6dbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI/UserCenter_atlas0.png using Guid(d83eef705968b492982a00b3baab6dbf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd0fd5c073c25596ad8df720aaef0856e') in 0.006191 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 2.258756 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI
  artifactKey: Guid(ff67de576b27c4acd91f5c3e0bb0e690) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FGUI using Guid(ff67de576b27c4acd91f5c3e0bb0e690) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '21c32667c9adb5b3d07e89a593e92226') in 0.002005 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 10.189833 seconds.
  path: Assets/GameApp/Textures/Level_Package3
  artifactKey: Guid(699338259406c41baab783132fca2bd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3 using Guid(699338259406c41baab783132fca2bd7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'a097a533eea7bf411e5cc371bd49720e') in 0.000296 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 44.235313 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/UI
  artifactKey: Guid(d72c1318f745b4ea18f169afe0579adc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/UI using Guid(d72c1318f745b4ea18f169afe0579adc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '8c5b7ce32853f744622aef697e6f9282') in 0.001580 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 4.931213 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Scripts/FGUI
  artifactKey: Guid(674e3da1de3fa4c48909d5d903f0ea7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Scripts/FGUI using Guid(674e3da1de3fa4c48909d5d903f0ea7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b20853e73a14ffe79825d76df32eb77c') in 0.001159 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 40.525017 seconds.
  path: Assets/GameApp/Textures/HomePage
  artifactKey: Guid(fbc4b2cf6d1b7124c8f71298103ea8f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/HomePage using Guid(fbc4b2cf6d1b7124c8f71298103ea8f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'da0e9f1968d13fb9fc8878d671364273') in 0.000302 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
[usbmuxd] Attached: 4 00008103-000D05103C31001E
========================================================================
Received Import Request.
  Time since last request: 241.979011 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Scripts/LogicalMatching_GameManage.cs
  artifactKey: Guid(0f94a9cd52a1f417696b8d5fd85351a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Scripts/LogicalMatching_GameManage.cs using Guid(0f94a9cd52a1f417696b8d5fd85351a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '8f2eb8b92ae9d3678092b596edc55211') in 0.001008 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.718728 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/Configs/Data_FD1_LevelPage.json
  artifactKey: Guid(e16125aad4cae407ebdfd91ca4755444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/Configs/Data_FD1_LevelPage.json using Guid(e16125aad4cae407ebdfd91ca4755444) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '16493278277b208de32e2e8cf9687897') in 0.001332 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.589 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.708 seconds
Domain Reload Profiling: 1298ms
	BeginReloadAssembly (373ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (708ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (166ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3516 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (205.7 KB). Loaded Objects now: 4080.
Memory consumption went from 167.5 MB to 167.3 MB.
Total: 5.226709 ms (FindLiveObjects: 0.109041 ms CreateObjectMapping: 0.062667 ms MarkObjects: 4.937125 ms  DeleteObjects: 0.117250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.542 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.719 seconds
Domain Reload Profiling: 1263ms
	BeginReloadAssembly (328ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (720ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (322ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.3 KB). Loaded Objects now: 4083.
Memory consumption went from 174.2 MB to 174.0 MB.
Total: 5.315666 ms (FindLiveObjects: 0.132541 ms CreateObjectMapping: 0.054417 ms MarkObjects: 5.008333 ms  DeleteObjects: 0.120000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.541 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1197ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (168ms)
		LoadAssemblies (169ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (655ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (303ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.4 KB). Loaded Objects now: 4086.
Memory consumption went from 180.7 MB to 180.5 MB.
Total: 5.097750 ms (FindLiveObjects: 0.123750 ms CreateObjectMapping: 0.061667 ms MarkObjects: 4.801166 ms  DeleteObjects: 0.110875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.314 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.454 seconds
Domain Reload Profiling: 769ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (134ms)
		LoadAssemblies (147ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (454ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (270ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (30ms)
			ProcessInitializeOnLoadAttributes (141ms)
			ProcessInitializeOnLoadMethodAttributes (78ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.4 KB). Loaded Objects now: 4089.
Memory consumption went from 187.1 MB to 186.9 MB.
Total: 4.803584 ms (FindLiveObjects: 0.087958 ms CreateObjectMapping: 0.048833 ms MarkObjects: 4.542459 ms  DeleteObjects: 0.123834 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 238.283778 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/Texture
  artifactKey: Guid(c30dc2713151349abac913f49dc8f668) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/Texture using Guid(c30dc2713151349abac913f49dc8f668) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '419489572d299f8e1ff18589e30ad032') in 0.001463 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 11.056440 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/Texture/V1
  artifactKey: Guid(811e9d48814c54fdc96a928c1436d612) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/Texture/V1 using Guid(811e9d48814c54fdc96a928c1436d612) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3733df2f4e389fe349700ccaad0eb71d') in 0.000649 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3190cb000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.560 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.623 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (160ms)
		LoadAssemblies (161ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (24ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (4ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (151ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3516 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.3 KB). Loaded Objects now: 4092.
Memory consumption went from 193.5 MB to 193.3 MB.
Total: 4.798416 ms (FindLiveObjects: 0.099166 ms CreateObjectMapping: 0.056167 ms MarkObjects: 4.540000 ms  DeleteObjects: 0.102791 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x31898f000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.305 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.499 seconds
Domain Reload Profiling: 805ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (121ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (499ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (159ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (173.4 KB). Loaded Objects now: 4095.
Memory consumption went from 200.2 MB to 200.0 MB.
Total: 5.133250 ms (FindLiveObjects: 0.132459 ms CreateObjectMapping: 0.054875 ms MarkObjects: 4.821250 ms  DeleteObjects: 0.123917 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x318bc7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.315 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 914ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (126ms)
		LoadAssemblies (141ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (321ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.4 KB). Loaded Objects now: 4098.
Memory consumption went from 206.7 MB to 206.5 MB.
Total: 5.326042 ms (FindLiveObjects: 0.119667 ms CreateObjectMapping: 0.059333 ms MarkObjects: 4.973625 ms  DeleteObjects: 0.172917 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x318c53000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.542 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.660 seconds
Domain Reload Profiling: 1203ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (174ms)
		LoadAssemblies (176ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (159ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.4 KB). Loaded Objects now: 4101.
Memory consumption went from 213.2 MB to 213.0 MB.
Total: 5.088292 ms (FindLiveObjects: 0.127209 ms CreateObjectMapping: 0.060500 ms MarkObjects: 4.777166 ms  DeleteObjects: 0.123125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x318cdf000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.310 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.488 seconds
Domain Reload Profiling: 799ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (127ms)
		LoadAssemblies (142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (489ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (295ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (154ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.5 KB). Loaded Objects now: 4104.
Memory consumption went from 219.6 MB to 219.5 MB.
Total: 5.410792 ms (FindLiveObjects: 0.130250 ms CreateObjectMapping: 0.056916 ms MarkObjects: 5.104250 ms  DeleteObjects: 0.118833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
[usbmuxd] Detached: 4 00008103-000D05103C31001E
========================================================================
Received Import Request.
  Time since last request: 2705.779984 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/游戏部分底图通用波点白调色.png
  artifactKey: Guid(8c22a27c168c942b6afcc052014d0c53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/游戏部分底图通用波点白调色.png using Guid(8c22a27c168c942b6afcc052014d0c53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3f798c7f0d7e578a2e21de22775333f3') in 0.028750 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 8.845505 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/bg-game.png
  artifactKey: Guid(8c22a27c168c942b6afcc052014d0c53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/bg-game.png using Guid(8c22a27c168c942b6afcc052014d0c53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3f6f2a9e57c9a691bd81919758490368') in 0.004328 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.033696 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/PiecesContainerBg.png
  artifactKey: Guid(73e2b1a67586a4d319723c276a2e1c17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/Common/PiecesContainerBg.png using Guid(73e2b1a67586a4d319723c276a2e1c17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ea5de7837bc33688233b27b7574cb266') in 0.004746 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 18.411234 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Sprite_Atlas/SA-SpotDifferent1-Common.spriteatlasv2
  artifactKey: Guid(3457f2855f6a741c3866fb00a83b3627) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Sprite_Atlas/SA-SpotDifferent1-Common.spriteatlasv2 using Guid(3457f2855f6a741c3866fb00a83b3627) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '8c05e661539859d4dc988be617d76441') in 0.001054 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 3.000898 seconds.
  path: Assets/-tk2d.asset
  artifactKey: Guid(ee6c5ae849f814dcb951fddb6ff3e6d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/-tk2d.asset using Guid(ee6c5ae849f814dcb951fddb6ff3e6d7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2ad02f6af2900564733c30330c84c0fe') in 0.001508 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 0.000348 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/0c353c62-effb-4923-9cd5-2424ea3b07ae.png
  artifactKey: Guid(693af05aefeff4cb7a4e4f928d424d48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/0c353c62-effb-4923-9cd5-2424ea3b07ae.png using Guid(693af05aefeff4cb7a4e4f928d424d48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'fe559c97fcc1097e6d28067531ff904b') in 0.007193 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV2/levelC_18/02.png
  artifactKey: Guid(1276872e212b0409799a4dca37b70a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV2/levelC_18/02.png using Guid(1276872e212b0409799a4dca37b70a4f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2d5b90f202fb205608976460c241118d') in 0.005268 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/GameApp/Textures/Maps/3.png
  artifactKey: Guid(f1d33546d10408844b679b632a24a43e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Maps/3.png using Guid(f1d33546d10408844b679b632a24a43e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'a5b9772caa0fffe62f425348ffdcc444') in 0.004020 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/GameApp/Textures/Maps/4.png
  artifactKey: Guid(80bb3728c48e2e94cbc1dd6b4a95e51a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Maps/4.png using Guid(80bb3728c48e2e94cbc1dd6b4a95e51a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '980f0f1e340b9ba1e38acb4e816ee453') in 0.003495 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV3/levelD_20/04.png
  artifactKey: Guid(33201175e938a4437aad12b37d893e74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV3/levelD_20/04.png using Guid(33201175e938a4437aad12b37d893e74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '93e2ee8f865de80d64996c067c0a7ad8') in 0.005613 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/GameApp/Textures/Maps/1.png
  artifactKey: Guid(c5ec82b749543ff4cbd23b274dc72def) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Maps/1.png using Guid(c5ec82b749543ff4cbd23b274dc72def) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '922dbcd386c534354117cfa7fb55b8fb') in 0.003630 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/1c1ecb12-3f0a-40bf-b1b2-3246692df19b.png
  artifactKey: Guid(eb91faae10b434aefb1051b1d3e3df48) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/1c1ecb12-3f0a-40bf-b1b2-3246692df19b.png using Guid(eb91faae10b434aefb1051b1d3e3df48) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '86ed1f072726ab27e715b21e735759b0') in 0.006333 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV2/levelC_20/04.png
  artifactKey: Guid(bc4492cd9824f4fae827c0a42abf06e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV2/levelC_20/04.png using Guid(bc4492cd9824f4fae827c0a42abf06e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0d48ef837709c8e48e108e50d1a05761') in 0.004947 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000123 seconds.
  path: Assets/TK2DROOT/tk2d_demo/demospritecollection/sprites/2dtoolkit_logo.png
  artifactKey: Guid(d4b13e5fdf4b0f540be8e00a364ddd35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TK2DROOT/tk2d_demo/demospritecollection/sprites/2dtoolkit_logo.png using Guid(d4b13e5fdf4b0f540be8e00a364ddd35) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '82a1502e3d010b9fd1bfa4026aae8d07') in 0.003405 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000106 seconds.
  path: Assets/GameApp/Textures/Maps/2.png
  artifactKey: Guid(589ef26953b35044f803403419defdfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Maps/2.png using Guid(589ef26953b35044f803403419defdfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '036c1bbfb8ff6c7a491a2157e15d03e3') in 0.003939 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV3/levelD_17/01.png
  artifactKey: Guid(7fb09cc72dc954b0bbf814605fa36847) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV3/levelD_17/01.png using Guid(7fb09cc72dc954b0bbf814605fa36847) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'a2c4af2cebaeb7a8b9c9203ee5c7bf50') in 0.004728 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/2cfcd200-a9b0-4f80-8267-1e743f510680.png
  artifactKey: Guid(1d3d1cf47e51a4eb9b4fe564d9cc92c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/2cfcd200-a9b0-4f80-8267-1e743f510680.png using Guid(1d3d1cf47e51a4eb9b4fe564d9cc92c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '60c52ec75de476caa8eea5687de342f3') in 0.006521 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV2/levelC_19/03.png
  artifactKey: Guid(e7d37927d9fe04d0db195a3c8ecb8a71) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV2/levelC_19/03.png using Guid(e7d37927d9fe04d0db195a3c8ecb8a71) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b33ba10d7f3e2861da75554d545e60cc') in 0.004838 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000132 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/0fdfe7c1-cff4-460a-ab92-dc34423d329f.png
  artifactKey: Guid(9344df51e49484dc79c6922a15220e0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/0fdfe7c1-cff4-460a-ab92-dc34423d329f.png using Guid(9344df51e49484dc79c6922a15220e0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '25ab47af4730fa695494068f66dca850') in 0.005549 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4b4bce64-f527-46c9-a9d8-3caf35f3cc6d.png
  artifactKey: Guid(1192bee8315794483839f67ada885253) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4b4bce64-f527-46c9-a9d8-3caf35f3cc6d.png using Guid(1192bee8315794483839f67ada885253) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '824ab18b89301e9c4941b1acd8be41c1') in 0.005374 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.129820 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4e6ae590-7cd7-4113-b0b4-47f81fc429b9.png
  artifactKey: Guid(d85a74a6f6d2e4afa8f8cd3c102e217a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4e6ae590-7cd7-4113-b0b4-47f81fc429b9.png using Guid(d85a74a6f6d2e4afa8f8cd3c102e217a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '95e3269a7c1f6685f7b36acad9e4fc56') in 0.006870 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/GameApp/Textures/Maps/5.png
  artifactKey: Guid(a2f24cd03b001b64e87db05e4c01c538) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Maps/5.png using Guid(a2f24cd03b001b64e87db05e4c01c538) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9d4cb62e00ede5e509f9003bffd1a52c') in 0.004677 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV3/levelD_21/05.png
  artifactKey: Guid(44315efb3ab4b4c818f5e1fd1b9b8e57) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV3/levelD_21/05.png using Guid(44315efb3ab4b4c818f5e1fd1b9b8e57) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0828feb34b5e5372aa034f70f1528809') in 0.005215 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.641669 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/5d6bad33-ed8f-4682-aee4-73a7e17f4d2f.png
  artifactKey: Guid(7a5cce540c34c42898bfdab708df73e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/5d6bad33-ed8f-4682-aee4-73a7e17f4d2f.png using Guid(7a5cce540c34c42898bfdab708df73e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '4beb75a2de2bd2f352419b4a5654baba') in 0.006675 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/08bdb88f-5f17-42fd-b6d6-e161c9a93433.png
  artifactKey: Guid(47b70993d82f44b7cb93a697b0747d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/08bdb88f-5f17-42fd-b6d6-e161c9a93433.png using Guid(47b70993d82f44b7cb93a697b0747d50) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'a9fa1d40f10fa457245e35c82d8332d6') in 0.005836 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/8b94780f-467b-463d-aa5b-c42941849e0b.png
  artifactKey: Guid(afb0c3a5a82a941ec84dc8c4174c18a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/8b94780f-467b-463d-aa5b-c42941849e0b.png using Guid(afb0c3a5a82a941ec84dc8c4174c18a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3568633f2a9565e4b976cded56bdfaab') in 0.005167 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/15a3a8f0-db3d-46f8-8572-578cbc95faf6.png
  artifactKey: Guid(d24d8d73a75084db088e1798448b3ec2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/15a3a8f0-db3d-46f8-8572-578cbc95faf6.png using Guid(d24d8d73a75084db088e1798448b3ec2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'e86a661dc056e157700b8a5066e00074') in 0.005522 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/679a07ca-c1b6-41f3-9b7e-768dce47f785.png
  artifactKey: Guid(72b5a902e7b5d4deaa58038d44bf548e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/679a07ca-c1b6-41f3-9b7e-768dce47f785.png using Guid(72b5a902e7b5d4deaa58038d44bf548e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '44437eb2eba958599e40e05d6cf57331') in 0.005153 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/430b61d8-9151-486e-93d3-f56047275a7f.png
  artifactKey: Guid(471382775d17f4b7aafb9a63ecc0fdcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/430b61d8-9151-486e-93d3-f56047275a7f.png using Guid(471382775d17f4b7aafb9a63ecc0fdcd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f249261d3f15ab6c6b1a535739f4d194') in 0.006103 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/21b0635c-8d5a-476c-acaa-1f5c17b90edc.png
  artifactKey: Guid(c60057ba54f814c2c93ee709a4ec5c31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/21b0635c-8d5a-476c-acaa-1f5c17b90edc.png using Guid(c60057ba54f814c2c93ee709a4ec5c31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9a73ba5e4a146131966a9699691accdb') in 0.005985 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/967dbc5d-0c9e-4253-aeda-a93d2aec94cc.png
  artifactKey: Guid(7eb24b6ceb58c48c096571ee22a32664) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/967dbc5d-0c9e-4253-aeda-a93d2aec94cc.png using Guid(7eb24b6ceb58c48c096571ee22a32664) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '5b3c5374d872678c49ba95358d4344a2') in 0.006578 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/69b4aef7-4bce-4107-b91e-477b210302de.png
  artifactKey: Guid(d159817ded7cd43768bc22f614152af8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/69b4aef7-4bce-4107-b91e-477b210302de.png using Guid(d159817ded7cd43768bc22f614152af8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '20373ecaa3d14cd0d9daeb49e9c2d04e') in 0.006469 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.362649 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/Texture/PaidAds/advise-Bg.png
  artifactKey: Guid(1b26f6a828e834c02b27088f42a8ec3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/Texture/PaidAds/advise-Bg.png using Guid(1b26f6a828e834c02b27088f42a8ec3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '34ef4dde8f765c42a9ac282db3454bfe') in 0.004396 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000109 seconds.
  path: Assets/TK2DROOT/tk2dUI_demo/Assets/Dark/Sprites/bg.png
  artifactKey: Guid(ed6eace5351574075b607cc35ce3ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TK2DROOT/tk2dUI_demo/Assets/Dark/Sprites/bg.png using Guid(ed6eace5351574075b607cc35ce3ba92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'bc53214f9ced5900793f62b73b4aedc2') in 0.003211 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000111 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FantasyForest/Images/bg1.png
  artifactKey: Guid(1812fb40781764bbbb9e5de06376cb75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FantasyForest/Images/bg1.png using Guid(1812fb40781764bbbb9e5de06376cb75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c0b89fdaa077513bc549d5d040958384') in 0.005261 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Mini_GameProjects/Feedback/bgColor.png
  artifactKey: Guid(3a1e4ae5f1c5a43f0b524df8711e7e7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Feedback/bgColor.png using Guid(3a1e4ae5f1c5a43f0b524df8711e7e7c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0049bd98ffaef7856dbef34dec6babd8') in 0.006293 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000116 seconds.
  path: Assets/GameApp/Textures/Win/bgg.png
  artifactKey: Guid(da4acebfa4ac56348a072dfebd8449b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Win/bgg.png using Guid(da4acebfa4ac56348a072dfebd8449b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6930d524cf69d2a1fdfb16a874106ee2') in 0.006548 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/GameApp/Textures/Win/bg.png
  artifactKey: Guid(c15edcb5ede1775449efd46264540cfb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Win/bg.png using Guid(c15edcb5ede1775449efd46264540cfb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0d517fe1e16c5eabe49b254d55ec36bb') in 0.004132 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/GameApp/Textures/Common/bg.png
  artifactKey: Guid(7b7394ef2d5fd4cf9b5a5e9aa407be7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Common/bg.png using Guid(7b7394ef2d5fd4cf9b5a5e9aa407be7e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '42aff710746ec719e7ebdb6a0e1aaf0c') in 0.003518 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj1.png
  artifactKey: Guid(d55ef42f7f88a4d9f83a12f0a03edd1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj1.png using Guid(d55ef42f7f88a4d9f83a12f0a03edd1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'a9ce504e945ba3660877838d47f70b27') in 0.004251 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000251 seconds.
  path: Assets/GameApp/Textures/Win/bggg.png
  artifactKey: Guid(63ccccc515688b64087afd1c3355da22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Win/bggg.png using Guid(63ccccc515688b64087afd1c3355da22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9fcab8d708cd15b2682ffb2c9da1aa40') in 0.005821 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj3a.png
  artifactKey: Guid(c1bef085c7b1446589b412742dfde1d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj3a.png using Guid(c1bef085c7b1446589b412742dfde1d0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b2467cc4c0a2eaf6a9418583859ccbcd') in 0.003739 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg4.png
  artifactKey: Guid(1b3a70091bedf40789bbd01d7826221a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg4.png using Guid(1b3a70091bedf40789bbd01d7826221a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'cc4608c069d29e6a7c0282151e50dd15') in 0.004799 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000312 seconds.
  path: Assets/TK2DROOT/tk2d_demo/demospritecollection/sprites/bg.png
  artifactKey: Guid(b5e1233ff1ab3d14a8b3e3910c4c8281) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TK2DROOT/tk2d_demo/demospritecollection/sprites/bg.png using Guid(b5e1233ff1ab3d14a8b3e3910c4c8281) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9d88dacb628e056e32d50277efd370d9') in 0.002311 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj3b.png
  artifactKey: Guid(d313946738c814c11aeabe73c3d383f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj3b.png using Guid(d313946738c814c11aeabe73c3d383f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '03422585fbdbd1c436415228a72b6c7d') in 0.004497 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.698553 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/L10/FBH-L10-bg-girl2.png
  artifactKey: Guid(7dd8b0d0769ef478cafabc6960a29bf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/L10/FBH-L10-bg-girl2.png using Guid(7dd8b0d0769ef478cafabc6960a29bf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9d33b8e56119fba8fcf5421dc16fa365') in 0.007144 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/GameApp/SettingScene/textMusicBg-tc.png
  artifactKey: Guid(ce1aca1f10065436c80e237b366c8826) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SettingScene/textMusicBg-tc.png using Guid(ce1aca1f10065436c80e237b366c8826) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ac189f8270a676f47d9dbef425067f6a') in 0.004967 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (174.9 KB). Loaded Objects now: 4149.
Memory consumption went from 164.9 MB to 164.7 MB.
Total: 5.198125 ms (FindLiveObjects: 0.106583 ms CreateObjectMapping: 0.052083 ms MarkObjects: 4.931500 ms  DeleteObjects: 0.107584 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 89, details: Operation aborted.
AssetImportWorker is now disconnected from the server
Process exiting

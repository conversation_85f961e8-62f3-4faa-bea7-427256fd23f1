Unity Editor version:    2022.3.61f1 (6c53ebaf375d)
Branch:                  2022.3/release
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.6.1 (Build 24G90)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        32768 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
-logFile
Logs/AssetImportWorker0.log
-srvPort
51333
Successfully changed project path to: /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8447434944]  Target information:

Player connection [8447434944]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1048217426 [EditorId] 1048217426 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8447434944]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 1048217426 [EditorId] 1048217426 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8447434944] Host joined multi-casting on [***********:54997]...
Player connection [8447434944] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 10.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M2 Max (high power)
Metal devices available: 1
0: Apple M2 Max (high power)
Using device Apple M2 Max (high power)
Initializing Metal device caps: Apple M2 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56070
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001473 seconds.
- Loaded All Assemblies, in  0.190 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
[usbmuxd] Attached: 5 e10b21bce1b5ea565990bc8410260e0a61fa2af4
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 56 ms
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.265 seconds
Domain Reload Profiling: 454ms
	BeginReloadAssembly (43ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (91ms)
		LoadAssemblies (44ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (89ms)
			TypeCache.Refresh (88ms)
				TypeCache.ScanAssembly (79ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (265ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (237ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (150ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (60ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.322 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.399 seconds
Domain Reload Profiling: 715ms
	BeginReloadAssembly (69ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (19ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (208ms)
		LoadAssemblies (141ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (94ms)
			TypeCache.Refresh (77ms)
				TypeCache.ScanAssembly (64ms)
			ScanForSourceGeneratedMonoScriptInfo (10ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (399ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (160ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.33 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3582 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (187.5 KB). Loaded Objects now: 4061.
Memory consumption went from 160.4 MB to 160.2 MB.
Total: 5.337667 ms (FindLiveObjects: 0.098375 ms CreateObjectMapping: 0.055125 ms MarkObjects: 5.050709 ms  DeleteObjects: 0.133041 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4535.460435 seconds.
  path: Assets/Mini_GameProjects/Android
  artifactKey: Guid(4b63f65a779334452bbc55b1f52d9afb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android using Guid(4b63f65a779334452bbc55b1f52d9afb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'be7b4728c27ab81d2186f6dfc5366c1d') in 0.003849 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 17.283814 seconds.
  path: Assets/Mini_GameProjects/PaidAndroidScene/A_PaidAds_OK.unity
  artifactKey: Guid(510c574140f104eb3be33a1c3b76205d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/PaidAndroidScene/A_PaidAds_OK.unity using Guid(510c574140f104eb3be33a1c3b76205d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '83d0bd9b42e9b0e3fae42834e52f4f97') in 0.003649 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.451558 seconds.
  path: Assets/Mini_GameProjects/PaidAndroidScene/A_PaidAds_OKSettings.lighting
  artifactKey: Guid(f6b0a1f51db9345209d22737fbafcc5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/PaidAndroidScene/A_PaidAds_OKSettings.lighting using Guid(f6b0a1f51db9345209d22737fbafcc5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
LightingSettings: switching bake backend from 2 to 0.
 -> (artifact id: '965aa672a9757204a6cd783179f1d7b3') in 0.010692 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 742.250664 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/Texture/V1/buy.png
  artifactKey: Guid(deb27ba942d2e489ba3bfd5eaaaf0da8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/Texture/V1/buy.png using Guid(deb27ba942d2e489ba3bfd5eaaaf0da8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'cb83a0261fcb5b5e2e685735fd624243') in 0.043015 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 190.499624 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/Texture/V1/challenge-title-tc.png
  artifactKey: Guid(e72aacc9e2c154502ae89690c49fe3ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/Texture/V1/challenge-title-tc.png using Guid(e72aacc9e2c154502ae89690c49fe3ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '7ee4a991da6069c7ec3a9f8e2b5107d2') in 0.005503 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (167.0 KB). Loaded Objects now: 4070.
Memory consumption went from 92.1 MB to 91.9 MB.
Total: 5.276958 ms (FindLiveObjects: 0.117417 ms CreateObjectMapping: 0.051291 ms MarkObjects: 5.034584 ms  DeleteObjects: 0.073250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4070.
Memory consumption went from 91.5 MB to 91.4 MB.
Total: 5.115125 ms (FindLiveObjects: 0.109834 ms CreateObjectMapping: 0.049167 ms MarkObjects: 4.881000 ms  DeleteObjects: 0.074875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.1 KB). Loaded Objects now: 4070.
Memory consumption went from 90.9 MB to 90.8 MB.
Total: 4.945750 ms (FindLiveObjects: 0.108583 ms CreateObjectMapping: 0.047083 ms MarkObjects: 4.698250 ms  DeleteObjects: 0.091709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3227c7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.548 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1251ms
	BeginReloadAssembly (367ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (157ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (317ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.5 KB). Loaded Objects now: 4072.
Memory consumption went from 151.8 MB to 151.6 MB.
Total: 5.239208 ms (FindLiveObjects: 0.128875 ms CreateObjectMapping: 0.051417 ms MarkObjects: 4.934542 ms  DeleteObjects: 0.123750 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.4 KB). Loaded Objects now: 4072.
Memory consumption went from 96.8 MB to 96.6 MB.
Total: 8.223916 ms (FindLiveObjects: 0.136417 ms CreateObjectMapping: 0.062167 ms MarkObjects: 7.911000 ms  DeleteObjects: 0.113958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.4 KB). Loaded Objects now: 4072.
Memory consumption went from 95.5 MB to 95.3 MB.
Total: 4.972000 ms (FindLiveObjects: 0.118833 ms CreateObjectMapping: 0.052375 ms MarkObjects: 4.720583 ms  DeleteObjects: 0.079791 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4072.
Memory consumption went from 94.9 MB to 94.7 MB.
Total: 5.110041 ms (FindLiveObjects: 0.110542 ms CreateObjectMapping: 0.048625 ms MarkObjects: 4.866209 ms  DeleteObjects: 0.084250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.2 KB). Loaded Objects now: 4072.
Memory consumption went from 94.1 MB to 93.9 MB.
Total: 5.027125 ms (FindLiveObjects: 0.122250 ms CreateObjectMapping: 0.060750 ms MarkObjects: 4.766667 ms  DeleteObjects: 0.077292 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4072.
Memory consumption went from 93.2 MB to 93.0 MB.
Total: 5.085333 ms (FindLiveObjects: 0.107834 ms CreateObjectMapping: 0.045292 ms MarkObjects: 4.847291 ms  DeleteObjects: 0.084375 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4072.
Memory consumption went from 92.6 MB to 92.4 MB.
Total: 4.896500 ms (FindLiveObjects: 0.110208 ms CreateObjectMapping: 0.048167 ms MarkObjects: 4.669250 ms  DeleteObjects: 0.068666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4072.
Memory consumption went from 91.8 MB to 91.6 MB.
Total: 8.459209 ms (FindLiveObjects: 0.126292 ms CreateObjectMapping: 0.056583 ms MarkObjects: 8.158459 ms  DeleteObjects: 0.117583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4072.
Memory consumption went from 90.8 MB to 90.7 MB.
Total: 5.235000 ms (FindLiveObjects: 0.104875 ms CreateObjectMapping: 0.047417 ms MarkObjects: 4.991708 ms  DeleteObjects: 0.090542 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 8.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4072.
Memory consumption went from 90.1 MB to 89.9 MB.
Total: 11.297625 ms (FindLiveObjects: 0.168666 ms CreateObjectMapping: 0.057667 ms MarkObjects: 10.927875 ms  DeleteObjects: 0.143000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ffd7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.671 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1439ms
	BeginReloadAssembly (433ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (198ms)
		LoadAssemblies (214ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (767ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (348ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (51ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (166ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.4 KB). Loaded Objects now: 4075.
Memory consumption went from 151.1 MB to 150.9 MB.
Total: 5.263042 ms (FindLiveObjects: 0.123625 ms CreateObjectMapping: 0.055792 ms MarkObjects: 4.965417 ms  DeleteObjects: 0.117958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ffd7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.321 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.487 seconds
Domain Reload Profiling: 809ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (135ms)
		LoadAssemblies (152ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (487ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (306ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (162ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.4 KB). Loaded Objects now: 4078.
Memory consumption went from 157.6 MB to 157.4 MB.
Total: 5.449292 ms (FindLiveObjects: 0.130584 ms CreateObjectMapping: 0.064958 ms MarkObjects: 5.126792 ms  DeleteObjects: 0.126416 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ffd7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.518 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1213ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (148ms)
		LoadAssemblies (166ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (695ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (314ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.4 KB). Loaded Objects now: 4081.
Memory consumption went from 164.1 MB to 163.9 MB.
Total: 5.141041 ms (FindLiveObjects: 0.123417 ms CreateObjectMapping: 0.058875 ms MarkObjects: 4.843042 ms  DeleteObjects: 0.115167 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 19.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (177.2 KB). Loaded Objects now: 4081.
Memory consumption went from 109.1 MB to 109.0 MB.
Total: 11.221625 ms (FindLiveObjects: 0.210625 ms CreateObjectMapping: 0.059166 ms MarkObjects: 10.799625 ms  DeleteObjects: 0.151667 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4081.
Memory consumption went from 107.8 MB to 107.6 MB.
Total: 5.066875 ms (FindLiveObjects: 0.114042 ms CreateObjectMapping: 0.048666 ms MarkObjects: 4.804209 ms  DeleteObjects: 0.099541 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4081.
Memory consumption went from 107.2 MB to 107.0 MB.
Total: 7.016584 ms (FindLiveObjects: 0.161625 ms CreateObjectMapping: 0.058750 ms MarkObjects: 6.695500 ms  DeleteObjects: 0.100334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 3224.142705 seconds.
  path: Assets/Plugins/Android/AndroidManifest.xml
  artifactKey: Guid(4fee6f8f93f3c425fb29c967789f6ac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Android/AndroidManifest.xml using Guid(4fee6f8f93f3c425fb29c967789f6ac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f5b4a75b0732fe4f23bfe6e7cca502d8') in 0.023452 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (151.0 KB). Loaded Objects now: 4081.
Memory consumption went from 106.3 MB to 106.1 MB.
Total: 9.787834 ms (FindLiveObjects: 0.191792 ms CreateObjectMapping: 0.052791 ms MarkObjects: 9.401375 ms  DeleteObjects: 0.141417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.2 KB). Loaded Objects now: 4081.
Memory consumption went from 105.6 MB to 105.4 MB.
Total: 5.030042 ms (FindLiveObjects: 0.108375 ms CreateObjectMapping: 0.045334 ms MarkObjects: 4.787791 ms  DeleteObjects: 0.088000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4081.
Memory consumption went from 105.0 MB to 104.8 MB.
Total: 9.133917 ms (FindLiveObjects: 0.143500 ms CreateObjectMapping: 0.057833 ms MarkObjects: 8.778833 ms  DeleteObjects: 0.153333 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17ffd7000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.573 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1276ms
	BeginReloadAssembly (376ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (156ms)
		LoadAssemblies (174ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (310ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (162ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.5 KB). Loaded Objects now: 4084.
Memory consumption went from 166.1 MB to 165.9 MB.
Total: 5.227334 ms (FindLiveObjects: 0.117542 ms CreateObjectMapping: 0.055125 ms MarkObjects: 4.935792 ms  DeleteObjects: 0.118500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4084.
Memory consumption went from 111.0 MB to 110.8 MB.
Total: 9.363708 ms (FindLiveObjects: 0.127500 ms CreateObjectMapping: 0.058334 ms MarkObjects: 9.068584 ms  DeleteObjects: 0.108833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.2 KB). Loaded Objects now: 4084.
Memory consumption went from 109.7 MB to 109.5 MB.
Total: 4.772042 ms (FindLiveObjects: 0.108167 ms CreateObjectMapping: 0.054625 ms MarkObjects: 4.502709 ms  DeleteObjects: 0.106083 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4084.
Memory consumption went from 108.7 MB to 108.6 MB.
Total: 5.905500 ms (FindLiveObjects: 0.139583 ms CreateObjectMapping: 0.108458 ms MarkObjects: 5.542667 ms  DeleteObjects: 0.114542 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4084.
Memory consumption went from 108.0 MB to 107.8 MB.
Total: 5.100083 ms (FindLiveObjects: 0.116917 ms CreateObjectMapping: 0.054166 ms MarkObjects: 4.820834 ms  DeleteObjects: 0.107583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4084.
Memory consumption went from 107.0 MB to 106.9 MB.
Total: 5.116375 ms (FindLiveObjects: 0.119667 ms CreateObjectMapping: 0.052667 ms MarkObjects: 4.840125 ms  DeleteObjects: 0.103250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4084.
Memory consumption went from 106.4 MB to 106.3 MB.
Total: 5.206208 ms (FindLiveObjects: 0.116875 ms CreateObjectMapping: 0.052333 ms MarkObjects: 4.937916 ms  DeleteObjects: 0.098666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4221.416488 seconds.
  path: Assets/Mini_GameProjects/Android/Paid/A_PaidAds.unity
  artifactKey: Guid(9ed4c7519666343418654485ac0cec8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/Android/Paid/A_PaidAds.unity using Guid(9ed4c7519666343418654485ac0cec8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd117e40cb3dfa390ef256c1acd201ab4') in 0.006522 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
[usbmuxd] Detached: 5 e10b21bce1b5ea565990bc8410260e0a61fa2af4
[usbmuxd] Attached: 41 00008103-000D05103C31001E
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.654 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.671 seconds
Domain Reload Profiling: 1326ms
	BeginReloadAssembly (417ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (193ms)
		LoadAssemblies (202ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (671ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (310ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.5 KB). Loaded Objects now: 4087.
Memory consumption went from 166.3 MB to 166.2 MB.
Total: 5.461750 ms (FindLiveObjects: 0.126792 ms CreateObjectMapping: 0.061542 ms MarkObjects: 5.136125 ms  DeleteObjects: 0.136833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 7912.085065 seconds.
  path: Assets/Plugins/Android/AndroidManifest.xml
  artifactKey: Guid(4fee6f8f93f3c425fb29c967789f6ac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Android/AndroidManifest.xml using Guid(4fee6f8f93f3c425fb29c967789f6ac7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c8ec23376f0030f7251c65b2d4849e1e') in 0.006927 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Import Request.
  Time since last request: 150.472697 seconds.
  path: Assets/Plugins/Android/baseProjectTemplate.gradle
  artifactKey: Guid(10f769c72ec004ad1a8bac141527af16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Plugins/Android/baseProjectTemplate.gradle using Guid(10f769c72ec004ad1a8bac141527af16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '53eb641c6d1e14ec2be9e0763f90a3ab') in 0.000566 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 1
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (151.0 KB). Loaded Objects now: 4087.
Memory consumption went from 111.1 MB to 110.9 MB.
Total: 8.354083 ms (FindLiveObjects: 0.152917 ms CreateObjectMapping: 0.061500 ms MarkObjects: 8.033417 ms  DeleteObjects: 0.105666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4087.
Memory consumption went from 110.4 MB to 110.2 MB.
Total: 5.202417 ms (FindLiveObjects: 0.119209 ms CreateObjectMapping: 0.055000 ms MarkObjects: 4.939083 ms  DeleteObjects: 0.088542 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 11.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4087.
Memory consumption went from 109.8 MB to 109.6 MB.
Total: 11.665416 ms (FindLiveObjects: 0.193708 ms CreateObjectMapping: 0.061334 ms MarkObjects: 11.151792 ms  DeleteObjects: 0.258291 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 2484.677337 seconds.
  path: Assets/GameApp/Scenes/levelA_4.unity
  artifactKey: Guid(f02cd8f104f6a4679ba8622e32c62c62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Scenes/levelA_4.unity using Guid(f02cd8f104f6a4679ba8622e32c62c62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f8a68614cc1f38223471a9f6a0dba80b') in 0.001177 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 1.379629 seconds.
  path: Assets/GameApp/Scenes/levelB_4.unity
  artifactKey: Guid(6d53abee8b7bf474382ecbdc6135532a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Scenes/levelB_4.unity using Guid(6d53abee8b7bf474382ecbdc6135532a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2e9111af511544e0f652011603143d94') in 0.000806 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 58.669094 seconds.
  path: Assets/GameApp/SplashScreen/SplashScreen.unity
  artifactKey: Guid(49d34a7405f00427d9f272697373b87f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SplashScreen/SplashScreen.unity using Guid(49d34a7405f00427d9f272697373b87f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd5eff388704eeeb25876837341633f59') in 0.000671 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.647 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.688 seconds
Domain Reload Profiling: 1336ms
	BeginReloadAssembly (420ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (189ms)
		LoadAssemblies (206ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (156ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.5 KB). Loaded Objects now: 4090.
Memory consumption went from 170.6 MB to 170.5 MB.
Total: 4.954042 ms (FindLiveObjects: 0.118250 ms CreateObjectMapping: 0.059500 ms MarkObjects: 4.668666 ms  DeleteObjects: 0.107125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 49.773721 seconds.
  path: Assets/GameApp/Prefabs/FindDifferent/Particles/CFXM2_PickupStar2.prefab
  artifactKey: Guid(0aefb77a860f5471baa96ac1c635e6b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Prefabs/FindDifferent/Particles/CFXM2_PickupStar2.prefab using Guid(0aefb77a860f5471baa96ac1c635e6b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f3ac4dbf991aa80c84f27a1cd8962ef3') in 0.039686 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f913000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.560 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.711 seconds
Domain Reload Profiling: 1272ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (159ms)
		LoadAssemblies (175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (321ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (168ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.7 KB). Loaded Objects now: 4098.
Memory consumption went from 177.7 MB to 177.5 MB.
Total: 5.412542 ms (FindLiveObjects: 0.128417 ms CreateObjectMapping: 0.055208 ms MarkObjects: 5.105542 ms  DeleteObjects: 0.122792 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f913000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.308 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.496 seconds
Domain Reload Profiling: 806ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (124ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (496ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (312ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (163ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.5 KB). Loaded Objects now: 4101.
Memory consumption went from 184.4 MB to 184.3 MB.
Total: 5.394292 ms (FindLiveObjects: 0.117417 ms CreateObjectMapping: 0.052375 ms MarkObjects: 5.103708 ms  DeleteObjects: 0.120333 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 428.357587 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Scenes/MatchByShape.unity
  artifactKey: Guid(192eed3dba18e4cd2b4abb4f148cdf99) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Scenes/MatchByShape.unity using Guid(192eed3dba18e4cd2b4abb4f148cdf99) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0b99054dcc987122ccb7166a85555554') in 0.002579 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 272.642366 seconds.
  path: Assets/Resources/EffectSounds/ui_menu_button_keystroke_01.wav
  artifactKey: Guid(63b229d6100e5499fba280af997d0ba9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/EffectSounds/ui_menu_button_keystroke_01.wav using Guid(63b229d6100e5499fba280af997d0ba9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ccee3e7bfbd5d7c0757c137b40f35068') in 0.054375 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 12.174428 seconds.
  path: Assets/Mini_GameFramework/UI/Prefabs/EndingPanel.prefab
  artifactKey: Guid(3706d8977f952462aa19dddd1fd4a255) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameFramework/UI/Prefabs/EndingPanel.prefab using Guid(3706d8977f952462aa19dddd1fd4a255) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'e91b00eefdf977dd361f4c220f436fad') in 0.021085 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 55
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f913000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.605 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.649 seconds
Domain Reload Profiling: 1254ms
	BeginReloadAssembly (383ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (180ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (649ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (153ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.4 KB). Loaded Objects now: 4106.
Memory consumption went from 191.4 MB to 191.2 MB.
Total: 5.088208 ms (FindLiveObjects: 0.123584 ms CreateObjectMapping: 0.063041 ms MarkObjects: 4.769084 ms  DeleteObjects: 0.131959 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.537 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1191ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (150ms)
		LoadAssemblies (166ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (654ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (296ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (157ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.7 KB). Loaded Objects now: 4109.
Memory consumption went from 198.1 MB to 197.9 MB.
Total: 4.840625 ms (FindLiveObjects: 0.115208 ms CreateObjectMapping: 0.058250 ms MarkObjects: 4.569083 ms  DeleteObjects: 0.097750 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.554 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.646 seconds
Domain Reload Profiling: 1200ms
	BeginReloadAssembly (336ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (646ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (157ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.6 KB). Loaded Objects now: 4112.
Memory consumption went from 204.6 MB to 204.4 MB.
Total: 5.074792 ms (FindLiveObjects: 0.115459 ms CreateObjectMapping: 0.061500 ms MarkObjects: 4.783542 ms  DeleteObjects: 0.113709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x17f887000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.329 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.510 seconds
Domain Reload Profiling: 839ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (142ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (510ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.6 KB). Loaded Objects now: 4115.
Memory consumption went from 211.1 MB to 210.9 MB.
Total: 5.349291 ms (FindLiveObjects: 0.129625 ms CreateObjectMapping: 0.054583 ms MarkObjects: 5.035167 ms  DeleteObjects: 0.129583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 

Unity Editor version:    2022.3.61f1 (6c53ebaf375d)
Branch:                  2022.3/release
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.6.1 (Build 24G90)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        32768 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
-logFile
Logs/AssetImportWorker1.log
-srvPort
51333
Successfully changed project path to: /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8447434944]  Target information:

Player connection [8447434944]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 548931319 [EditorId] 548931319 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8447434944]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 548931319 [EditorId] 548931319 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8447434944] Host joined multi-casting on [***********:54997]...
Player connection [8447434944] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 10.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M2 Max (high power)
Metal devices available: 1
0: Apple M2 Max (high power)
Using device Apple M2 Max (high power)
Initializing Metal device caps: Apple M2 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56072
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001434 seconds.
- Loaded All Assemblies, in  0.189 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
[usbmuxd] Attached: 5 e10b21bce1b5ea565990bc8410260e0a61fa2af4
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 67 ms
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.265 seconds
Domain Reload Profiling: 454ms
	BeginReloadAssembly (43ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (90ms)
		LoadAssemblies (44ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (88ms)
			TypeCache.Refresh (87ms)
				TypeCache.ScanAssembly (79ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (265ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (238ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (154ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (58ms)
			ProcessInitializeOnLoadMethodAttributes (22ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.318 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.373 seconds
Domain Reload Profiling: 688ms
	BeginReloadAssembly (70ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (17ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (202ms)
		LoadAssemblies (144ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (88ms)
			TypeCache.Refresh (73ms)
				TypeCache.ScanAssembly (60ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (374ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (282ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (29ms)
			ProcessInitializeOnLoadAttributes (152ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3582 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (203.5 KB). Loaded Objects now: 4061.
Memory consumption went from 160.4 MB to 160.2 MB.
Total: 5.391916 ms (FindLiveObjects: 0.123125 ms CreateObjectMapping: 0.065458 ms MarkObjects: 5.074167 ms  DeleteObjects: 0.128750 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 4526.740521 seconds.
  path: Assets/GameApp/AdsControl
  artifactKey: Guid(537614e6b7bff4bac9107fa8676ffaf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/AdsControl using Guid(537614e6b7bff4bac9107fa8676ffaf9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c15ad5981ad2d8060c73cd1540b22dba') in 0.001478 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (166.9 KB). Loaded Objects now: 4061.
Memory consumption went from 89.3 MB to 89.2 MB.
Total: 5.084125 ms (FindLiveObjects: 0.131209 ms CreateObjectMapping: 0.055000 ms MarkObjects: 4.813791 ms  DeleteObjects: 0.083791 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4061.
Memory consumption went from 88.8 MB to 88.7 MB.
Total: 4.877500 ms (FindLiveObjects: 0.109000 ms CreateObjectMapping: 0.047459 ms MarkObjects: 4.643625 ms  DeleteObjects: 0.076833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (177.3 KB). Loaded Objects now: 4061.
Memory consumption went from 88.2 MB to 88.1 MB.
Total: 4.858292 ms (FindLiveObjects: 0.108375 ms CreateObjectMapping: 0.047333 ms MarkObjects: 4.624667 ms  DeleteObjects: 0.077084 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x316787000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.550 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.702 seconds
Domain Reload Profiling: 1253ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (157ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (702ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (316ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (166ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
Refreshing native plugins compatible for Editor in 2.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.5 KB). Loaded Objects now: 4064.
Memory consumption went from 149.4 MB to 149.2 MB.
Total: 4.959917 ms (FindLiveObjects: 0.127625 ms CreateObjectMapping: 0.056208 ms MarkObjects: 4.657750 ms  DeleteObjects: 0.118125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.4 KB). Loaded Objects now: 4064.
Memory consumption went from 94.4 MB to 94.2 MB.
Total: 8.897792 ms (FindLiveObjects: 0.130583 ms CreateObjectMapping: 0.055292 ms MarkObjects: 8.607541 ms  DeleteObjects: 0.103833 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.4 KB). Loaded Objects now: 4064.
Memory consumption went from 93.1 MB to 92.9 MB.
Total: 4.779625 ms (FindLiveObjects: 0.110292 ms CreateObjectMapping: 0.060584 ms MarkObjects: 4.500209 ms  DeleteObjects: 0.108042 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (177.5 KB). Loaded Objects now: 4064.
Memory consumption went from 92.5 MB to 92.3 MB.
Total: 5.026125 ms (FindLiveObjects: 0.102709 ms CreateObjectMapping: 0.044833 ms MarkObjects: 4.805458 ms  DeleteObjects: 0.072916 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.78 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (177.4 KB). Loaded Objects now: 4064.
Memory consumption went from 91.7 MB to 91.6 MB.
Total: 4.771166 ms (FindLiveObjects: 0.123292 ms CreateObjectMapping: 0.057500 ms MarkObjects: 4.497834 ms  DeleteObjects: 0.092000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4064.
Memory consumption went from 90.8 MB to 90.6 MB.
Total: 5.125417 ms (FindLiveObjects: 0.108167 ms CreateObjectMapping: 0.042542 ms MarkObjects: 4.892792 ms  DeleteObjects: 0.081417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.4 KB). Loaded Objects now: 4064.
Memory consumption went from 90.2 MB to 90.0 MB.
Total: 4.895208 ms (FindLiveObjects: 0.109333 ms CreateObjectMapping: 0.047166 ms MarkObjects: 4.658167 ms  DeleteObjects: 0.079917 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 10.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4064.
Memory consumption went from 89.4 MB to 89.3 MB.
Total: 8.695459 ms (FindLiveObjects: 0.136792 ms CreateObjectMapping: 0.062916 ms MarkObjects: 8.387000 ms  DeleteObjects: 0.108417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4064.
Memory consumption went from 88.5 MB to 88.3 MB.
Total: 4.953000 ms (FindLiveObjects: 0.107000 ms CreateObjectMapping: 0.050250 ms MarkObjects: 4.689875 ms  DeleteObjects: 0.105584 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 8.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4064.
Memory consumption went from 87.7 MB to 87.5 MB.
Total: 11.455292 ms (FindLiveObjects: 0.200042 ms CreateObjectMapping: 0.058625 ms MarkObjects: 11.023542 ms  DeleteObjects: 0.172625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x312b07000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.676 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.774 seconds
Domain Reload Profiling: 1451ms
	BeginReloadAssembly (434ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (201ms)
		LoadAssemblies (216ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (774ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (357ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (63ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.4 KB). Loaded Objects now: 4067.
Memory consumption went from 148.8 MB to 148.6 MB.
Total: 5.254084 ms (FindLiveObjects: 0.123084 ms CreateObjectMapping: 0.053958 ms MarkObjects: 4.947208 ms  DeleteObjects: 0.129459 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x312b97000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.323 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.474 seconds
Domain Reload Profiling: 798ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (136ms)
		LoadAssemblies (152ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (475ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (297ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (154ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.5 KB). Loaded Objects now: 4070.
Memory consumption went from 155.3 MB to 155.1 MB.
Total: 5.121708 ms (FindLiveObjects: 0.095958 ms CreateObjectMapping: 0.049166 ms MarkObjects: 4.858542 ms  DeleteObjects: 0.117666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x312c53000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.519 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1214ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (152ms)
		LoadAssemblies (167ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (694ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (314ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.5 KB). Loaded Objects now: 4073.
Memory consumption went from 161.7 MB to 161.6 MB.
Total: 5.124750 ms (FindLiveObjects: 0.123417 ms CreateObjectMapping: 0.049875 ms MarkObjects: 4.838041 ms  DeleteObjects: 0.113084 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 19.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (177.4 KB). Loaded Objects now: 4073.
Memory consumption went from 106.8 MB to 106.6 MB.
Total: 11.809916 ms (FindLiveObjects: 0.170167 ms CreateObjectMapping: 0.054500 ms MarkObjects: 11.436750 ms  DeleteObjects: 0.147875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.2 KB). Loaded Objects now: 4073.
Memory consumption went from 105.5 MB to 105.3 MB.
Total: 5.240708 ms (FindLiveObjects: 0.116167 ms CreateObjectMapping: 0.051458 ms MarkObjects: 4.984042 ms  DeleteObjects: 0.088916 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.5 KB). Loaded Objects now: 4073.
Memory consumption went from 104.9 MB to 104.7 MB.
Total: 6.546084 ms (FindLiveObjects: 0.132625 ms CreateObjectMapping: 0.054292 ms MarkObjects: 6.244708 ms  DeleteObjects: 0.114084 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4073.
Memory consumption went from 104.1 MB to 103.9 MB.
Total: 10.188000 ms (FindLiveObjects: 0.166625 ms CreateObjectMapping: 0.053625 ms MarkObjects: 9.828875 ms  DeleteObjects: 0.138458 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.6 KB). Loaded Objects now: 4073.
Memory consumption went from 103.2 MB to 103.0 MB.
Total: 5.018667 ms (FindLiveObjects: 0.107958 ms CreateObjectMapping: 0.049250 ms MarkObjects: 4.764542 ms  DeleteObjects: 0.096584 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 5.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4073.
Memory consumption went from 102.6 MB to 102.4 MB.
Total: 7.316542 ms (FindLiveObjects: 0.148125 ms CreateObjectMapping: 0.057042 ms MarkObjects: 7.000208 ms  DeleteObjects: 0.110625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x312b07000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.574 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.703 seconds
Domain Reload Profiling: 1279ms
	BeginReloadAssembly (375ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (160ms)
		LoadAssemblies (177ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (703ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (313ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.7 KB). Loaded Objects now: 4076.
Memory consumption went from 163.7 MB to 163.5 MB.
Total: 5.207584 ms (FindLiveObjects: 0.119084 ms CreateObjectMapping: 0.054500 ms MarkObjects: 4.919167 ms  DeleteObjects: 0.114375 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.6 KB). Loaded Objects now: 4076.
Memory consumption went from 108.6 MB to 108.4 MB.
Total: 9.687458 ms (FindLiveObjects: 0.132208 ms CreateObjectMapping: 0.060000 ms MarkObjects: 9.388667 ms  DeleteObjects: 0.106250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.4 KB). Loaded Objects now: 4076.
Memory consumption went from 107.2 MB to 107.1 MB.
Total: 5.214500 ms (FindLiveObjects: 0.122167 ms CreateObjectMapping: 0.058375 ms MarkObjects: 4.936541 ms  DeleteObjects: 0.096958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4076.
Memory consumption went from 106.3 MB to 106.2 MB.
Total: 5.666042 ms (FindLiveObjects: 0.148167 ms CreateObjectMapping: 0.060708 ms MarkObjects: 5.331708 ms  DeleteObjects: 0.124917 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.3 KB). Loaded Objects now: 4076.
Memory consumption went from 105.6 MB to 105.4 MB.
Total: 5.110667 ms (FindLiveObjects: 0.121833 ms CreateObjectMapping: 0.055833 ms MarkObjects: 4.838333 ms  DeleteObjects: 0.094459 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.3 KB). Loaded Objects now: 4076.
Memory consumption went from 104.6 MB to 104.5 MB.
Total: 5.128416 ms (FindLiveObjects: 0.119334 ms CreateObjectMapping: 0.054166 ms MarkObjects: 4.862667 ms  DeleteObjects: 0.091916 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (185.5 KB). Loaded Objects now: 4076.
Memory consumption went from 104.0 MB to 103.9 MB.
Total: 5.289459 ms (FindLiveObjects: 0.118000 ms CreateObjectMapping: 0.053416 ms MarkObjects: 5.017667 ms  DeleteObjects: 0.100250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
[usbmuxd] Detached: 5 e10b21bce1b5ea565990bc8410260e0a61fa2af4
[usbmuxd] Attached: 41 00008103-000D05103C31001E
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.656 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.670 seconds
Domain Reload Profiling: 1326ms
	BeginReloadAssembly (415ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (197ms)
		LoadAssemblies (201ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (30ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (9ms)
			ScanForSourceGeneratedMonoScriptInfo (6ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (308ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.6 KB). Loaded Objects now: 4079.
Memory consumption went from 164.1 MB to 163.9 MB.
Total: 5.475458 ms (FindLiveObjects: 0.125500 ms CreateObjectMapping: 0.064250 ms MarkObjects: 5.156459 ms  DeleteObjects: 0.128875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 9.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.4 KB). Loaded Objects now: 4079.
Memory consumption went from 109.1 MB to 108.9 MB.
Total: 8.126583 ms (FindLiveObjects: 0.146959 ms CreateObjectMapping: 0.059125 ms MarkObjects: 7.808917 ms  DeleteObjects: 0.111166 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.3 KB). Loaded Objects now: 4079.
Memory consumption went from 107.8 MB to 107.6 MB.
Total: 4.930792 ms (FindLiveObjects: 0.108125 ms CreateObjectMapping: 0.049875 ms MarkObjects: 4.681375 ms  DeleteObjects: 0.091166 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 11.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (153.2 KB). Loaded Objects now: 4079.
Memory consumption went from 107.2 MB to 107.0 MB.
Total: 11.255542 ms (FindLiveObjects: 0.210208 ms CreateObjectMapping: 0.060333 ms MarkObjects: 10.793334 ms  DeleteObjects: 0.191375 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.647 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.696 seconds
Domain Reload Profiling: 1343ms
	BeginReloadAssembly (421ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (186ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (696ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (303ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (160ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.7 KB). Loaded Objects now: 4082.
Memory consumption went from 168.2 MB to 168.1 MB.
Total: 5.004917 ms (FindLiveObjects: 0.115417 ms CreateObjectMapping: 0.057208 ms MarkObjects: 4.706917 ms  DeleteObjects: 0.124667 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.558 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.710 seconds
Domain Reload Profiling: 1269ms
	BeginReloadAssembly (358ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (160ms)
		LoadAssemblies (175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (711ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.7 KB). Loaded Objects now: 4085.
Memory consumption went from 174.7 MB to 174.6 MB.
Total: 5.261458 ms (FindLiveObjects: 0.122875 ms CreateObjectMapping: 0.056583 ms MarkObjects: 4.951875 ms  DeleteObjects: 0.129875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.300 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.481 seconds
Domain Reload Profiling: 782ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (118ms)
		LoadAssemblies (133ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.8 KB). Loaded Objects now: 4088.
Memory consumption went from 181.2 MB to 181.1 MB.
Total: 5.367000 ms (FindLiveObjects: 0.118166 ms CreateObjectMapping: 0.053875 ms MarkObjects: 5.074834 ms  DeleteObjects: 0.119666 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.606 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.650 seconds
Domain Reload Profiling: 1257ms
	BeginReloadAssembly (381ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (183ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (650ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (295ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (155ms)
			ProcessInitializeOnLoadMethodAttributes (85ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.7 KB). Loaded Objects now: 4091.
Memory consumption went from 187.7 MB to 187.5 MB.
Total: 5.501209 ms (FindLiveObjects: 0.123083 ms CreateObjectMapping: 0.062458 ms MarkObjects: 5.191250 ms  DeleteObjects: 0.124209 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.537 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.653 seconds
Domain Reload Profiling: 1190ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (151ms)
		LoadAssemblies (168ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (653ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (293ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (157ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.7 KB). Loaded Objects now: 4094.
Memory consumption went from 194.2 MB to 194.0 MB.
Total: 4.842417 ms (FindLiveObjects: 0.112959 ms CreateObjectMapping: 0.057083 ms MarkObjects: 4.559500 ms  DeleteObjects: 0.112416 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.554 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.648 seconds
Domain Reload Profiling: 1202ms
	BeginReloadAssembly (336ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (648ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.80 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.7 KB). Loaded Objects now: 4097.
Memory consumption went from 200.7 MB to 200.5 MB.
Total: 5.147834 ms (FindLiveObjects: 0.114166 ms CreateObjectMapping: 0.061250 ms MarkObjects: 4.865291 ms  DeleteObjects: 0.106709 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x311737000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.328 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.497 seconds
Domain Reload Profiling: 825ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (141ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (497ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (303ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (158ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3518 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.5 KB). Loaded Objects now: 4100.
Memory consumption went from 207.2 MB to 207.0 MB.
Total: 5.350958 ms (FindLiveObjects: 0.127834 ms CreateObjectMapping: 0.083750 ms MarkObjects: 5.019125 ms  DeleteObjects: 0.119958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 

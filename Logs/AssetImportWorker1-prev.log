Unity Editor version:    2022.3.61f1 (6c53ebaf375d)
Branch:                  2022.3/release
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.6.1 (Build 24G90)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        32768 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
-logFile
Logs/AssetImportWorker1.log
-srvPort
53818
Successfully changed project path to: /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
/Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8535531712]  Target information:

Player connection [8535531712]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4152722490 [EditorId] 4152722490 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712]  * "[IP] ************** [Port] 0 [Flags] 2 [Guid] 4152722490 [EditorId] 4152722490 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 4152722490 [EditorId] 4152722490 [Version] 1048832 [Id] OSXEditor(0,Mac-F2deMac-Studio.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8535531712] Host joined multi-casting on [***********:54997]...
Player connection [8535531712] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
Refreshing native plugins compatible for Editor in 20.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.61f1 (6c53ebaf375d)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Game_Projects/huawei_apps/FindDifferent_Huawei/Assets
GfxDevice: creating device client; threaded=0; jobified=0
 preferred device: Apple M2 Max (high power)
Metal devices available: 1
0: Apple M2 Max (high power)
Using device Apple M2 Max (high power)
Initializing Metal device caps: Apple M2 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56413
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/2022.3.61f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004456 seconds.
- Loaded All Assemblies, in  0.212 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
[usbmuxd] Attached: 3 00008101-00191DE22011401E
[usbmuxd] Attached: 2 e10b21bce1b5ea565990bc8410260e0a61fa2af4
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 56 ms
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.266 seconds
Domain Reload Profiling: 479ms
	BeginReloadAssembly (62ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (93ms)
		LoadAssemblies (63ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (91ms)
			TypeCache.Refresh (90ms)
				TypeCache.ScanAssembly (82ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (267ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (239ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (151ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (1ms)
			ProcessInitializeOnLoadAttributes (61ms)
			ProcessInitializeOnLoadMethodAttributes (23ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.380 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.376 seconds
Domain Reload Profiling: 757ms
	BeginReloadAssembly (66ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (17ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (276ms)
		LoadAssemblies (217ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (85ms)
			TypeCache.Refresh (71ms)
				TypeCache.ScanAssembly (59ms)
			ScanForSourceGeneratedMonoScriptInfo (9ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (377ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (284ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (152ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/2022.3.61f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3581 Unused Serialized files (Serialized files now loaded: 0)
Unloading 37 unused Assets / (211.8 KB). Loaded Objects now: 4060.
Memory consumption went from 160.3 MB to 160.1 MB.
Total: 5.173041 ms (FindLiveObjects: 0.108375 ms CreateObjectMapping: 0.058083 ms MarkObjects: 4.870125 ms  DeleteObjects: 0.135750 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 7366.871489 seconds.
  path: Assets/GameApp/Textures/Level_Select
  artifactKey: Guid(1b32270b0d4f9445c84fd75f71e1db8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Select using Guid(1b32270b0d4f9445c84fd75f71e1db8b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '463827da190a12ebe257a845a3a1db94') in 0.001129 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321c87000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.301 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.469 seconds
Domain Reload Profiling: 771ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (116ms)
		LoadAssemblies (133ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (469ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (289ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (154ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3516 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (198.0 KB). Loaded Objects now: 4063.
Memory consumption went from 151.5 MB to 151.3 MB.
Total: 4.809834 ms (FindLiveObjects: 0.098000 ms CreateObjectMapping: 0.046250 ms MarkObjects: 4.556417 ms  DeleteObjects: 0.108834 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x321c87000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.524 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 1234ms
	BeginReloadAssembly (324ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (160ms)
		LoadAssemblies (175ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (320ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (167ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.6 KB). Loaded Objects now: 4066.
Memory consumption went from 158.2 MB to 158.0 MB.
Total: 5.357000 ms (FindLiveObjects: 0.124167 ms CreateObjectMapping: 0.054166 ms MarkObjects: 5.049375 ms  DeleteObjects: 0.128834 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
[usbmuxd] Attached: 4 00008103-000D05103C31001E
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.588 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.709 seconds
Domain Reload Profiling: 1297ms
	BeginReloadAssembly (373ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (172ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (709ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (322ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (168ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (197.9 KB). Loaded Objects now: 4069.
Memory consumption went from 164.6 MB to 164.5 MB.
Total: 5.397583 ms (FindLiveObjects: 0.121125 ms CreateObjectMapping: 0.048792 ms MarkObjects: 5.097625 ms  DeleteObjects: 0.129334 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x320647000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.543 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.720 seconds
Domain Reload Profiling: 1264ms
	BeginReloadAssembly (329ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (174ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (720ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (323ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.7 KB). Loaded Objects now: 4072.
Memory consumption went from 171.1 MB to 170.9 MB.
Total: 5.346542 ms (FindLiveObjects: 0.120333 ms CreateObjectMapping: 0.050208 ms MarkObjects: 5.044875 ms  DeleteObjects: 0.130291 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.541 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.655 seconds
Domain Reload Profiling: 1197ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (168ms)
		LoadAssemblies (169ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (26ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (656ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (304ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (159ms)
			ProcessInitializeOnLoadMethodAttributes (87ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.6 KB). Loaded Objects now: 4075.
Memory consumption went from 177.6 MB to 177.4 MB.
Total: 5.133542 ms (FindLiveObjects: 0.112917 ms CreateObjectMapping: 0.056667 ms MarkObjects: 4.841250 ms  DeleteObjects: 0.122041 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.325 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.454 seconds
Domain Reload Profiling: 780ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (140ms)
		LoadAssemblies (155ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (454ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (281ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (147ms)
			ProcessInitializeOnLoadMethodAttributes (82ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.7 KB). Loaded Objects now: 4078.
Memory consumption went from 184.1 MB to 183.9 MB.
Total: 5.088042 ms (FindLiveObjects: 0.111083 ms CreateObjectMapping: 0.054792 ms MarkObjects: 4.816625 ms  DeleteObjects: 0.105083 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.561 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.622 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (162ms)
		LoadAssemblies (163ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (623ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (152ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.7 KB). Loaded Objects now: 4081.
Memory consumption went from 190.5 MB to 190.4 MB.
Total: 4.709833 ms (FindLiveObjects: 0.113834 ms CreateObjectMapping: 0.060333 ms MarkObjects: 4.430709 ms  DeleteObjects: 0.104500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x16e30b000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.301 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.513 seconds
Domain Reload Profiling: 815ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (121ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (513ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (316ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.20 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (189.8 KB). Loaded Objects now: 4084.
Memory consumption went from 197.0 MB to 196.8 MB.
Total: 5.354250 ms (FindLiveObjects: 0.132208 ms CreateObjectMapping: 0.056333 ms MarkObjects: 5.038792 ms  DeleteObjects: 0.126250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.313 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 912ms
	BeginReloadAssembly (145ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (127ms)
		LoadAssemblies (142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (322ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (36ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (190.0 KB). Loaded Objects now: 4087.
Memory consumption went from 203.5 MB to 203.3 MB.
Total: 5.300666 ms (FindLiveObjects: 0.121792 ms CreateObjectMapping: 0.058375 ms MarkObjects: 5.001084 ms  DeleteObjects: 0.118875 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.542 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.663 seconds
Domain Reload Profiling: 1206ms
	BeginReloadAssembly (331ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (171ms)
		LoadAssemblies (173ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (24ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (8ms)
			ScanForSourceGeneratedMonoScriptInfo (5ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (663ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (306ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (33ms)
			ProcessInitializeOnLoadAttributes (161ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.6 KB). Loaded Objects now: 4090.
Memory consumption went from 210.0 MB to 209.8 MB.
Total: 5.308916 ms (FindLiveObjects: 0.117833 ms CreateObjectMapping: 0.059875 ms MarkObjects: 5.013625 ms  DeleteObjects: 0.117083 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.313 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.510 seconds
Domain Reload Profiling: 824ms
	BeginReloadAssembly (143ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (131ms)
		LoadAssemblies (146ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (510ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (323ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (169ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.6 KB). Loaded Objects now: 4093.
Memory consumption went from 216.4 MB to 216.3 MB.
Total: 5.466334 ms (FindLiveObjects: 0.131208 ms CreateObjectMapping: 0.051416 ms MarkObjects: 5.149292 ms  DeleteObjects: 0.133959 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
[usbmuxd] Detached: 4 00008103-000D05103C31001E
========================================================================
Received Import Request.
  Time since last request: 3648.473855 seconds.
  path: Assets/ProCamera2D/Examples/Platformer/Textures/0.png
  artifactKey: Guid(ed6174cfc03074afa9523d9500a2c7c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/ProCamera2D/Examples/Platformer/Textures/0.png using Guid(ed6174cfc03074afa9523d9500a2c7c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '387c1fef29535899397dbc8b7cabea56') in 0.050640 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/GameApp/Textures/Local_Levels/Level102/02.png
  artifactKey: Guid(a68c9d05e35fc46a8845102709b3bde1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Local_Levels/Level102/02.png using Guid(a68c9d05e35fc46a8845102709b3bde1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '63d859031f90061aff528e90caaf3e84') in 0.006649 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV3/levelD_19/03.png
  artifactKey: Guid(05ecace986dd045d5a5cc1826664f607) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV3/levelD_19/03.png using Guid(05ecace986dd045d5a5cc1826664f607) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'edfae2622d1da3e3e4ec538855c3e378') in 0.004577 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/GameApp/Textures/Local_Levels/Level101/01.png
  artifactKey: Guid(3dd5059504d5f4a00b23b934484f004c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Local_Levels/Level101/01.png using Guid(3dd5059504d5f4a00b23b934484f004c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'bed9cccada0aa113d814adb4831a0140') in 0.007055 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV2/levelC_17/01.png
  artifactKey: Guid(4f7ba75b37f264808ad8b492e01db8c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV2/levelC_17/01.png using Guid(4f7ba75b37f264808ad8b492e01db8c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'c545a8561bc8ae81fa05f7f1ce310bd2') in 0.005450 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV3/levelD_18/02.png
  artifactKey: Guid(d39fe958c8d714915b7febd1d71a979c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV3/levelD_18/02.png using Guid(d39fe958c8d714915b7febd1d71a979c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'cf8bfa81c2a1bf8164f2d9a801f681ca') in 0.004639 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.134832 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4eaeb227-3ed1-4e42-aa3e-01a5d03de17a.png
  artifactKey: Guid(405a7c44616e1428ab66020782d93eb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/4eaeb227-3ed1-4e42-aa3e-01a5d03de17a.png using Guid(405a7c44616e1428ab66020782d93eb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0dc5f85a08890c4e77368c7d64cbc1f3') in 0.006258 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/GameApp/Textures/Level_Package3/LV2/levelC_21/05.png
  artifactKey: Guid(de86cf40053c1425f8015280db10557a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/Textures/Level_Package3/LV2/levelC_21/05.png using Guid(de86cf40053c1425f8015280db10557a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6387bb44e48b2c1d79b20305cb610c88') in 0.005523 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/5bf6c829-b13e-4c44-8a6f-15288c7a84fb.png
  artifactKey: Guid(67629640ed9a344d4b9fd5b0ae18580f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/5bf6c829-b13e-4c44-8a6f-15288c7a84fb.png using Guid(67629640ed9a344d4b9fd5b0ae18580f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9727ef04560d67a7c780ade1b93462e6') in 0.006273 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 1.641010 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/7ca67d63-0332-4deb-a7c5-715f7bbe30e5.png
  artifactKey: Guid(c9fa1e747ac574cf48e30abde2e07525) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/7ca67d63-0332-4deb-a7c5-715f7bbe30e5.png using Guid(c9fa1e747ac574cf48e30abde2e07525) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '7129217f0e0e135acd4be00f611760f4') in 0.006738 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/8e413bd7-ab31-40da-aeef-9e5687dec8f2.png
  artifactKey: Guid(7a242c63a026e4f7ab83ba344da48dd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/8e413bd7-ab31-40da-aeef-9e5687dec8f2.png using Guid(7a242c63a026e4f7ab83ba344da48dd8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'f8ea8a27ff837c051e9f62da0d3f3752') in 0.005636 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/90ed56ea-6053-4282-b0f2-1c64a43f30d3.png
  artifactKey: Guid(e1a82f703875241218bb163d020950ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/90ed56ea-6053-4282-b0f2-1c64a43f30d3.png using Guid(e1a82f703875241218bb163d020950ba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0d200d5b2ae7d9a7a31f47e2ab4f1302') in 0.006389 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/632e3ae6-9ce5-4855-b0b7-1a7e7a82f131.png
  artifactKey: Guid(da031ead6f65e4bfab81ff0bf14c17d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/632e3ae6-9ce5-4855-b0b7-1a7e7a82f131.png using Guid(da031ead6f65e4bfab81ff0bf14c17d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '3f7ba928d5ba3d71b93c9ea1eae1144b') in 0.005632 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/32d808e5-89d4-4a2a-aedb-66e436003d27.png
  artifactKey: Guid(0d1d2f1ae55d34cc6bc44e052dbb3ed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/32d808e5-89d4-4a2a-aedb-66e436003d27.png using Guid(0d1d2f1ae55d34cc6bc44e052dbb3ed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '43e6b391787297d548c73f4403c9b578') in 0.005473 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/862a20f5-609c-40a5-809d-a93b4c4b7d94.png
  artifactKey: Guid(b967ddd0cb3064afdb8bf2b84c407408) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/862a20f5-609c-40a5-809d-a93b4c4b7d94.png using Guid(b967ddd0cb3064afdb8bf2b84c407408) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'e434bca98339bf3286783d6722c16a01') in 0.006137 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/40c9dd52-c0c5-4725-acb3-dd4fdbc4ffdb.png
  artifactKey: Guid(400d0147d32b749aeaf6ca9939376e1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/40c9dd52-c0c5-4725-acb3-dd4fdbc4ffdb.png using Guid(400d0147d32b749aeaf6ca9939376e1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '91c1f379ab327eedf1cd4d6fdc4ed595') in 0.005771 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/7d23c48b-f021-4497-bbca-da128708ef89.png
  artifactKey: Guid(c8e21dd5278734a18a40f8e4a886c752) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/7d23c48b-f021-4497-bbca-da128708ef89.png using Guid(c8e21dd5278734a18a40f8e4a886c752) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '7bbedefd049bf331a6b6d1f1c561ae4b') in 0.005666 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/208a47a5-8ba9-43c9-935f-0e65493cacea.png
  artifactKey: Guid(319af2ab45d654fa191c361b23763dfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Resources/FindTheMissingPart/Images/208a47a5-8ba9-43c9-935f-0e65493cacea.png using Guid(319af2ab45d654fa191c361b23763dfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0f1fd206106e7c5a1373b6e5dda6406d') in 0.006234 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.362865 seconds.
  path: Assets/Mini_GameProjects/PaidAndroidScene/Texture/advise-Bg.png
  artifactKey: Guid(25cfedf2d02d4430195bec7c205383a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Mini_GameProjects/PaidAndroidScene/Texture/advise-Bg.png using Guid(25cfedf2d02d4430195bec7c205383a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'bb5e0d095cece270ed7b275d0ffadf13') in 0.006135 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/GameApp/SettingScene/bg2.png
  artifactKey: Guid(6a12be12d6f184ee18f910b5531e04ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SettingScene/bg2.png using Guid(6a12be12d6f184ee18f910b5531e04ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd11d13add21978fb6dd5d622fcca8e03') in 0.005436 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/Common/Bg_HintWhiteBox.png
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/Common/Bg_HintWhiteBox.png using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'e2f9dd42e08d6e613aaadceef04fdf3a') in 0.005575 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SunlitForest/Images/bgh.png
  artifactKey: Guid(3a212f35288bc4f9e9065cceb5b4b002) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SunlitForest/Images/bgh.png using Guid(3a212f35288bc4f9e9065cceb5b4b002) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '69f6fe95c44bfee10924ade5806cfced') in 0.004018 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/GameApp/SettingScene/bg.png
  artifactKey: Guid(91df5dc0b6f47478f9a36328684e14ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SettingScene/bg.png using Guid(91df5dc0b6f47478f9a36328684e14ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '4ffa46eeff6d86400381662386ba9464') in 0.003902 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/TK2DROOT/tk2d/Editor/Resources/tk2dSkin/<EMAIL>
  artifactKey: Guid(24ad4415b46924f9298dd72dc6d4d03a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TK2DROOT/tk2d/Editor/Resources/tk2dSkin/<EMAIL> using Guid(24ad4415b46924f9298dd72dc6d4d03a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0174e5eac48380d15b67c044397cd1a6') in 0.002678 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.000103 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg3.png
  artifactKey: Guid(8e688816ba2314b5db8e69e3c34d5b07) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg3.png using Guid(8e688816ba2314b5db8e69e3c34d5b07) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '1e24c0611e2d92eaf18400cb411b55f6') in 0.004828 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FantasyForest/Images/bg.png
  artifactKey: Guid(4bdbebe9831be43fea381908306c6c34) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FantasyForest/Images/bg.png using Guid(4bdbebe9831be43fea381908306c6c34) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '55d2058083227f4870de90e588afb6ec') in 0.004672 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/NightForest/Images/bgh.png
  artifactKey: Guid(47a052dea98e543c287634e5255a0c52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/NightForest/Images/bgh.png using Guid(47a052dea98e543c287634e5255a0c52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '9ddb70dec35a39d0e4a0c17d0fa975e7') in 0.004455 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj2.png
  artifactKey: Guid(3a520ff457f334cce80abd2bf51cf5fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj2.png using Guid(3a520ff457f334cce80abd2bf51cf5fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '27bc8e7adfb2a2cb0bab35e1af0b49d2') in 0.004990 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj.png
  artifactKey: Guid(5a3cafb8b4efe44bf94799cfc5610f5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/SummerForest/Images/bgj.png using Guid(5a3cafb8b4efe44bf94799cfc5610f5c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0d8ecbef5a59293e16e594621062c4b0') in 0.003836 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000145 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg2.png
  artifactKey: Guid(74609cea369ca477a9ca42aed5861765) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/ClickTheObjectAsInstructed/Blackground/FallForest/Images/bg2.png using Guid(74609cea369ca477a9ca42aed5861765) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6a564085e5e2c233152d9d1293b1deb8') in 0.004216 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/Common/Bg_YellowBorder.png
  artifactKey: Guid(6de6a09e6b0454e438df7d35c074db87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/LearningSuite/ThemesApps/SpotDifferent1/Images/FindByHint/Common/Bg_YellowBorder.png using Guid(6de6a09e6b0454e438df7d35c074db87) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '50b539e7b9b2d3f95bf6136014c29ffa') in 0.004484 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.698167 seconds.
  path: Assets/GameApp/SettingScene/textMusicBg-en.png
  artifactKey: Guid(d8f34036770384678b30469f1dd7c45e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SettingScene/textMusicBg-en.png using Guid(d8f34036770384678b30469f1dd7c45e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2557cd6a0ebc68cf02e9ff95215e10ce') in 0.006382 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/GameApp/SettingScene/textMusicBg-sc.png
  artifactKey: Guid(05aa143f295ef4e939990a15fe112efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameApp/SettingScene/textMusicBg-sc.png using Guid(05aa143f295ef4e939990a15fe112efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd516f77e9400115058d3393e8eeb0a04') in 0.005794 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 3
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (167.5 KB). Loaded Objects now: 4134.
Memory consumption went from 164.0 MB to 163.9 MB.
Total: 5.505084 ms (FindLiveObjects: 0.128833 ms CreateObjectMapping: 0.054709 ms MarkObjects: 5.211000 ms  DeleteObjects: 0.110250 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 7 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (169.4 KB). Loaded Objects now: 4134.
Memory consumption went from 163.5 MB to 163.4 MB.
Total: 4.995375 ms (FindLiveObjects: 0.113958 ms CreateObjectMapping: 0.049542 ms MarkObjects: 4.740834 ms  DeleteObjects: 0.090625 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.302 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.491 seconds
Domain Reload Profiling: 794ms
	BeginReloadAssembly (144ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (118ms)
		LoadAssemblies (132ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (491ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (302ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (159ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.7 KB). Loaded Objects now: 4136.
Memory consumption went from 224.0 MB to 223.8 MB.
Total: 5.407000 ms (FindLiveObjects: 0.123583 ms CreateObjectMapping: 0.052750 ms MarkObjects: 5.111166 ms  DeleteObjects: 0.119125 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.309 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.465 seconds
Domain Reload Profiling: 776ms
	BeginReloadAssembly (156ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (116ms)
		LoadAssemblies (131ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (10ms)
			TypeCache.Refresh (4ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (466ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (293ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (17ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (31ms)
			ProcessInitializeOnLoadAttributes (155ms)
			ProcessInitializeOnLoadMethodAttributes (86ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.6 KB). Loaded Objects now: 4139.
Memory consumption went from 230.5 MB to 230.3 MB.
Total: 5.028125 ms (FindLiveObjects: 0.116250 ms CreateObjectMapping: 0.058542 ms MarkObjects: 4.752666 ms  DeleteObjects: 0.100417 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 422.412170 seconds.
  path: Assets/TK2DROOT/tk2d/Editor/Resources/tk2dSkin/animtrigger.png
  artifactKey: Guid(a6723cdb1d0c54e21b26ed3f2abafdb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TK2DROOT/tk2d/Editor/Resources/tk2dSkin/animtrigger.png using Guid(a6723cdb1d0c54e21b26ed3f2abafdb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'b7a71ba699fe99b0d3aa0d7cdd9d8734') in 0.058625 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 2
========================================================================
Received Import Request.
  Time since last request: 0.980165 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Blood/Variants/Green/CFX2_BloodPuddle_Add Green.prefab
  artifactKey: Guid(69114c0d9ce1e2145987a6810121c0dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Blood/Variants/Green/CFX2_BloodPuddle_Add Green.prefab using Guid(69114c0d9ce1e2145987a6810121c0dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd4fe89bf84fe1fa6c2402b20b64bac16') in 0.069215 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Space/Variants/CFX4_Space Thruster A (Green).prefab
  artifactKey: Guid(6394b33368aa0ef46a47aa03f3855b32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Space/Variants/CFX4_Space Thruster A (Green).prefab using Guid(6394b33368aa0ef46a47aa03f3855b32) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '25f48f03a4088700593cd0dd4ca45ebf') in 0.022371 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 13
========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Space/Variants/CFX4_Plasma Ball (Green-Purple, CFX Blend).prefab
  artifactKey: Guid(006e53bba485db64f8af64731781ac23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Space/Variants/CFX4_Plasma Ball (Green-Purple, CFX Blend).prefab using Guid(006e53bba485db64f8af64731781ac23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '8e5a698e8548b367229c84c79de51435') in 0.005055 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Magic/Variants/CFX4 Fairy Dust (Green).prefab
  artifactKey: Guid(309c99fdd741cc9439822edae6a5d7a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Magic/Variants/CFX4 Fairy Dust (Green).prefab using Guid(309c99fdd741cc9439822edae6a5d7a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ca08f2c9de7c9ff091c3819873d42892') in 0.005728 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 16
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Fire/Variants/CFX4 Flamme Round (Green, CFX Blend).prefab
  artifactKey: Guid(252480c3cd5fe924a9604c6a0ac1b5a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Fire/Variants/CFX4 Flamme Round (Green, CFX Blend).prefab using Guid(252480c3cd5fe924a9604c6a0ac1b5a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '5ef8341dddd1309857a602262460bc7c') in 0.028608 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 20
========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Electric/Variants/CFX4 Sparks Explosion (Green).prefab
  artifactKey: Guid(b792026e1487a0349a2e40cfde55bef4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Electric/Variants/CFX4 Sparks Explosion (Green).prefab using Guid(b792026e1487a0349a2e40cfde55bef4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '81079a9ed2a617dfca9326feb2fc1763') in 0.004763 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountainHeavy_Add Green.prefab
  artifactKey: Guid(33919d515489d2940a757315071a7174) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountainHeavy_Add Green.prefab using Guid(33919d515489d2940a757315071a7174) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '6b7198c0ca6862284534a0247b2ec929') in 0.004106 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 19
========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountainHeavy Green.prefab
  artifactKey: Guid(7b63238953201ba46b9043a34f3c13be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountainHeavy Green.prefab using Guid(7b63238953201ba46b9043a34f3c13be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'afeae125bed0f717a5bb6bd9b1aebab9') in 0.004969 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 19
========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountain Green.prefab
  artifactKey: Guid(885e03ffe20909e44bcc1830759ac329) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodFountain Green.prefab using Guid(885e03ffe20909e44bcc1830759ac329) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ba17c456e01806abf7b09bbe5fe04875') in 0.003372 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodHeavy_Add Green.prefab
  artifactKey: Guid(7ee7500a19d40dd4593b0c4ca7d34344) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodHeavy_Add Green.prefab using Guid(7ee7500a19d40dd4593b0c4ca7d34344) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '230c24e7fc027c7e69487aa69cc8677d') in 0.003310 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle_Add Green.prefab
  artifactKey: Guid(f48c24bb6e97d794eab2ffcb42ee33e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle_Add Green.prefab using Guid(f48c24bb6e97d794eab2ffcb42ee33e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '04e55602699f61de1ceb14b79f525b31') in 0.003522 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Explosions/Variants/CFX4 Explosion SoftEdge Air (Green).prefab
  artifactKey: Guid(ee24f1eaef0d51144add93b32d225267) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Explosions/Variants/CFX4 Explosion SoftEdge Air (Green).prefab using Guid(ee24f1eaef0d51144add93b32d225267) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '0ab0094dd0244518b95af187c3d9dd3c') in 0.006199 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 32
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Misc/Variants/CFX4 Hit C (Green).prefab
  artifactKey: Guid(57edd4a5f531a2a42a4c393f6282d500) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Misc/Variants/CFX4 Hit C (Green).prefab using Guid(57edd4a5f531a2a42a4c393f6282d500) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '313d6122ca522b4ea773b75e71a9372a') in 0.004195 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Misc/Variants/CFX4 Hit Paint C (Green).prefab
  artifactKey: Guid(8289cd1a65533d642ab66ea145d5c8b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Misc/Variants/CFX4 Hit Paint C (Green).prefab using Guid(8289cd1a65533d642ab66ea145d5c8b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '885f6f09aa749181ec144a314e97fcd9') in 0.004468 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Skull & Ghosts Effects/Color Variants/CFX2_SkullRotate Green.prefab
  artifactKey: Guid(b1218921ddb01f7409beb056fe84f125) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Skull & Ghosts Effects/Color Variants/CFX2_SkullRotate Green.prefab using Guid(b1218921ddb01f7409beb056fe84f125) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'd4985ba34372e936c77fcfc6340c4462') in 0.006250 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 25
========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_Blood_Add Green.prefab
  artifactKey: Guid(07fb0403ce1c62f41a4269d871187454) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_Blood_Add Green.prefab using Guid(07fb0403ce1c62f41a4269d871187454) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'ecc5f7c7b6164d6b36dc16f50836137c') in 0.003434 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 15
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle Green.prefab
  artifactKey: Guid(d0a8aeaf21fcaad46a0053f372e4f62a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle Green.prefab using Guid(d0a8aeaf21fcaad46a0053f372e4f62a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '825ea8756fe6865e32063d51677d2564') in 0.003417 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Misc/Variants/CFX2_PowerAura Green.prefab
  artifactKey: Guid(4566cab1df63daa4aa67e15b3df4ed29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Misc/Variants/CFX2_PowerAura Green.prefab using Guid(4566cab1df63daa4aa67e15b3df4ed29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '5ed1f5e70e5d373b4140532539ae0ad2') in 0.005799 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 21
========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Skull & Ghosts Effects/Color Variants/CFX2_Soul Green.prefab
  artifactKey: Guid(dfb5c97d1f30fb04e84e486581aac006) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs/Skull & Ghosts Effects/Color Variants/CFX2_Soul Green.prefab using Guid(dfb5c97d1f30fb04e84e486581aac006) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '40140fe6cdef8036677e4d448a85d2ec') in 0.006784 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 30
========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Explosions/Variants/CFX4 Wave Explosion (Green).prefab
  artifactKey: Guid(cc39a47f22d6890468febc2157117072) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX4 Prefabs/Explosions/Variants/CFX4 Wave Explosion (Green).prefab using Guid(cc39a47f22d6890468febc2157117072) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: 'efc2dc38a0abb7ceb82e0ffe669ffae9') in 0.006020 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 33
========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle_Multiply Green.prefab
  artifactKey: Guid(effd29a4919c05a4abb19a241a2921b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/JMO Assets/Cartoon FX/CFX2 Prefabs (Mobile)/Blood/Variants/Green/CFXM2_BloodPuddle_Multiply Green.prefab using Guid(effd29a4919c05a4abb19a241a2921b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 12 workers.
 -> (artifact id: '2904f12d3314f82eeb726a207e39f70b') in 0.044242 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 9
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6 unused Assets / (167.3 KB). Loaded Objects now: 4151.
Memory consumption went from 176.0 MB to 175.8 MB.
Total: 4.964334 ms (FindLiveObjects: 0.099875 ms CreateObjectMapping: 0.040209 ms MarkObjects: 4.744208 ms  DeleteObjects: 0.079584 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:2d/spriteatlas-import-mode: 02000000000000000000000000000000 -> 01000000000000000000000000000000
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.344 seconds
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.724 seconds
Domain Reload Profiling: 1069ms
	BeginReloadAssembly (158ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (148ms)
		LoadAssemblies (162ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (12ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (724ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (316ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (165ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 2.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (181.8 KB). Loaded Objects now: 4153.
Memory consumption went from 236.9 MB to 236.7 MB.
Total: 5.497000 ms (FindLiveObjects: 0.137667 ms CreateObjectMapping: 0.060083 ms MarkObjects: 5.175334 ms  DeleteObjects: 0.123458 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Thread 0x3206d3000 may have been prematurely finalized
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.ScriptableObject:CreateInstance (System.Type) (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:35)
UnityEngine.ScriptableObject:CreateInstance<UnityEditor.ScriptReloadProperties> () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/ScriptableObject.bindings.cs:41)
UnityEditor.ScriptReloadProperties:Store () (at /Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs:48)

[/Users/<USER>/build/output/unity/unity/Editor/Mono/ScriptReloadProperties.cs line 48]

Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.322 seconds
OnLevelWasLoaded was found on SoundManager
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
OnLevelWasLoaded was found on DemoPart1
This message has been deprecated and will be removed in a later version of Unity.
Add a delegate to SceneManager.sceneLoaded instead to get notifications after scene loading has completed
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.717 seconds
Domain Reload Profiling: 1040ms
	BeginReloadAssembly (151ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (133ms)
		LoadAssemblies (146ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (11ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (717ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (323ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (34ms)
			ProcessInitializeOnLoadAttributes (168ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Refreshing native plugins compatible for Editor in 3.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 3517 Unused Serialized files (Serialized files now loaded: 0)
Unloading 28 unused Assets / (165.9 KB). Loaded Objects now: 4156.
Memory consumption went from 243.3 MB to 243.2 MB.
Total: 5.502750 ms (FindLiveObjects: 0.132084 ms CreateObjectMapping: 0.058708 ms MarkObjects: 5.180500 ms  DeleteObjects: 0.130958 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:CustomObjectIndexerAttribute: 9a22284fe3817be447336de3de66b15e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: 3918e3fc78b5a79bad01e8451be0beb8 -> 
  custom:framework-osx-AVFoundation: e770b220cccbd017edd2c1fefb359320 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
TcpMessagingSession - receive error: operation aborted. errorcode: 89, details: Operation aborted.
AssetImportWorker is now disconnected from the server
Process exiting
